<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Mon Aug 18 14:23:24 CST 2025 -->
<title>CipherUtil (License Client 1.0.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-08-18">
<meta name="description" content="declaration: package: com.robin.license.client.security, class: CipherUtil">
<meta name="generator" content="javadoc/ClassWriterImpl">
<link rel="stylesheet" type="text/css" href="../../../../../stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="../../../../../script.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-dir/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">var pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="../../../../../index.html">概览</a></li>
<li><a href="package-summary.html">程序包</a></li>
<li class="nav-bar-cell1-rev">类</li>
<li><a href="class-use/CipherUtil.html">使用</a></li>
<li><a href="package-tree.html">树</a></li>
<li><a href="../../../../../index-all.html">索引</a></li>
<li><a href="../../../../../help-doc.html#class">帮助</a></li>
</ul>
<ul class="sub-nav-list-small">
<li>
<p>概要:</p>
<ul>
<li>嵌套</li>
<li><a href="#field-summary">字段</a></li>
<li><a href="#constructor-summary">构造器</a></li>
<li><a href="#method-summary">方法</a></li>
</ul>
</li>
<li>
<p>详细资料:</p>
<ul>
<li><a href="#field-detail">字段</a></li>
<li><a href="#constructor-detail">构造器</a></li>
<li><a href="#method-detail">方法</a></li>
</ul>
</li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list">
<ul class="sub-nav-list">
<li>概要:&nbsp;</li>
<li>嵌套&nbsp;|&nbsp;</li>
<li><a href="#field-summary">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-summary">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-summary">方法</a></li>
</ul>
<ul class="sub-nav-list">
<li>详细资料:&nbsp;</li>
<li><a href="#field-detail">字段</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor-detail">构造器</a>&nbsp;|&nbsp;</li>
<li><a href="#method-detail">方法</a></li>
</ul>
</div>
<div class="nav-list-search"><a href="../../../../../search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="sub-title"><span class="package-label-in-type">程序包</span>&nbsp;<a href="package-summary.html">com.robin.license.client.security</a></div>
<h1 title="类 CipherUtil" class="title">类 CipherUtil</h1>
</div>
<div class="inheritance" title="继承树"><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">java.lang.Object</a>
<div class="inheritance">com.robin.license.client.security.CipherUtil</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">CipherUtil</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></span></div>
<div class="block">加密工具类
 提供字节加密解密、配置文件生成和参数获取功能</div>
<dl class="notes">
<dt>版本:</dt>
<dd>1.0.0</dd>
<dt>作者:</dt>
<dd>Robin</dd>
</dl>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>字段概要</h2>
<div class="caption"><span>字段</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">字段</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color"><code>static final <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="#DEFAULTKEY" class="member-name-link">DEFAULTKEY</a></code></div>
<div class="col-last even-row-color">
<div class="block">默认加密密钥</div>
</div>
<div class="col-first odd-row-color"><code>static final byte[]</code></div>
<div class="col-second odd-row-color"><code><a href="#m_datapadding" class="member-name-link">m_datapadding</a></code></div>
<div class="col-last odd-row-color">
<div class="block">数据填充字节，用于分隔数据项</div>
</div>
<div class="col-first even-row-color"><code>static final byte[]</code></div>
<div class="col-second even-row-color"><code><a href="#m_ending" class="member-name-link">m_ending</a></code></div>
<div class="col-last even-row-color">
<div class="block">文件结束标记</div>
</div>
<div class="col-first odd-row-color"><code>static final byte[]</code></div>
<div class="col-second odd-row-color"><code><a href="#mzHeader" class="member-name-link">mzHeader</a></code></div>
<div class="col-last odd-row-color">
<div class="block">EXE文件头标识，用于伪装成exe文件</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>构造器概要</h2>
<div class="caption"><span>构造器</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">构造器</div>
<div class="table-header col-last">说明</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">CipherUtil</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>方法概要</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">所有方法</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">静态方法</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">实例方法</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">具体方法</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel">
<div class="summary-table three-column-summary" aria-labelledby="method-summary-table-tab0">
<div class="table-header col-first">修饰符和类型</div>
<div class="table-header col-second">方法</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#decryptByte(byte%5B%5D,byte%5B%5D)" class="member-name-link">decryptByte</a><wbr>(byte[]&nbsp;encryptedData,
 byte[]&nbsp;key)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">解密字节数组</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#encryptByte(byte%5B%5D,byte%5B%5D)" class="member-name-link">encryptByte</a><wbr>(byte[]&nbsp;data,
 byte[]&nbsp;key)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">加密字节数组</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#generateFromProperties(java.lang.String,java.lang.String)" class="member-name-link">generateFromProperties</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;configFilePath,
 <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">从properties文件生成keyword.dat</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#generateKeywordFile(java.lang.String,java.util.Map)" class="member-name-link">generateKeywordFile</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputFile,
 <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;configMap)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">从配置映射生成keyword.dat文件</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getInstance()" class="member-name-link">getInstance</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">获取单例实例</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#interactiveGenerate()" class="member-name-link">interactiveGenerate</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">交互式生成配置文件</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#main(java.lang.String%5B%5D)" class="member-name-link">main</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>[]&nbsp;args)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">主方法</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#retKeyword(java.lang.Integer)" class="member-name-link">retKeyword</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;key)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">根据数字ID获取关键字值</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static byte[]</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#xorDecrypt(byte%5B%5D,byte%5B%5D)" class="member-name-link">xorDecrypt</a><wbr>(byte[]&nbsp;encryptedData,
 byte[]&nbsp;key)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">简单的XOR解密（作为备用方案）</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static byte[]</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#xorEncrypt(byte%5B%5D,byte%5B%5D)" class="member-name-link">xorEncrypt</a><wbr>(byte[]&nbsp;data,
 byte[]&nbsp;key)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">简单的XOR加密（作为备用方案）</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">从类继承的方法&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html" title="java.lang中的类或接口" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#clone()" title="java.lang中的类或接口" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="java.lang中的类或接口" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#finalize()" title="java.lang中的类或接口" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#getClass()" title="java.lang中的类或接口" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#hashCode()" title="java.lang中的类或接口" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#notify()" title="java.lang中的类或接口" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#notifyAll()" title="java.lang中的类或接口" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#toString()" title="java.lang中的类或接口" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#wait()" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#wait(long)" title="java.lang中的类或接口" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="java.lang中的类或接口" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>字段详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="DEFAULTKEY">
<h3>DEFAULTKEY</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">DEFAULTKEY</span></div>
<div class="block">默认加密密钥</div>
<dl class="notes">
<dt>另请参阅:</dt>
<dd>
<ul class="tag-list">
<li><a href="../../../../../constant-values.html#com.robin.license.client.security.CipherUtil.DEFAULTKEY">常量字段值</a></li>
</ul>
</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="m_datapadding">
<h3>m_datapadding</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">m_datapadding</span></div>
<div class="block">数据填充字节，用于分隔数据项</div>
</section>
</li>
<li>
<section class="detail" id="m_ending">
<h3>m_ending</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">m_ending</span></div>
<div class="block">文件结束标记</div>
</section>
</li>
<li>
<section class="detail" id="mzHeader">
<h3>mzHeader</h3>
<div class="member-signature"><span class="modifiers">public static final</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">mzHeader</span></div>
<div class="block">EXE文件头标识，用于伪装成exe文件</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>构造器详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>CipherUtil</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">CipherUtil</span>()</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>方法详细资料</h2>
<ul class="member-list">
<li>
<section class="detail" id="encryptByte(byte[],byte[])">
<h3>encryptByte</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">encryptByte</span><wbr><span class="parameters">(byte[]&nbsp;data,
 byte[]&nbsp;key)</span></div>
<div class="block">加密字节数组</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>data</code> - 要加密的数据</dd>
<dd><code>key</code> - 加密密钥</dd>
<dt>返回:</dt>
<dd>加密后的字节数组</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="decryptByte(byte[],byte[])">
<h3>decryptByte</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">decryptByte</span><wbr><span class="parameters">(byte[]&nbsp;encryptedData,
 byte[]&nbsp;key)</span></div>
<div class="block">解密字节数组</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>encryptedData</code> - 要解密的数据</dd>
<dd><code>key</code> - 解密密钥</dd>
<dt>返回:</dt>
<dd>解密后的字节数组</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="xorEncrypt(byte[],byte[])">
<h3>xorEncrypt</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">xorEncrypt</span><wbr><span class="parameters">(byte[]&nbsp;data,
 byte[]&nbsp;key)</span></div>
<div class="block">简单的XOR加密（作为备用方案）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>data</code> - 要加密的数据</dd>
<dd><code>key</code> - 加密密钥</dd>
<dt>返回:</dt>
<dd>加密后的字节数组</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="xorDecrypt(byte[],byte[])">
<h3>xorDecrypt</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">byte[]</span>&nbsp;<span class="element-name">xorDecrypt</span><wbr><span class="parameters">(byte[]&nbsp;encryptedData,
 byte[]&nbsp;key)</span></div>
<div class="block">简单的XOR解密（作为备用方案）</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>encryptedData</code> - 要解密的数据</dd>
<dd><code>key</code> - 解密密钥</dd>
<dt>返回:</dt>
<dd>解密后的字节数组</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="getInstance()">
<h3>getInstance</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></span>&nbsp;<span class="element-name">getInstance</span>()</div>
<div class="block">获取单例实例</div>
</section>
</li>
<li>
<section class="detail" id="retKeyword(java.lang.Integer)">
<h3>retKeyword</h3>
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a></span>&nbsp;<span class="element-name">retKeyword</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>&nbsp;key)</span></div>
<div class="block">根据数字ID获取关键字值</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>key</code> - 数字ID</dd>
<dt>返回:</dt>
<dd>对应的值</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateKeywordFile(java.lang.String,java.util.Map)">
<h3>generateKeywordFile</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateKeywordFile</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputFile,
 <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/util/Map.html" title="java.util中的类或接口" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Integer.html" title="java.lang中的类或接口" class="external-link">Integer</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&gt;&nbsp;configMap)</span></div>
<div class="block">从配置映射生成keyword.dat文件</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>outputFile</code> - 输出文件路径</dd>
<dd><code>configMap</code> - 配置映射</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="generateFromProperties(java.lang.String,java.lang.String)">
<h3>generateFromProperties</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">generateFromProperties</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;configFilePath,
 <a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>&nbsp;outputPath)</span></div>
<div class="block">从properties文件生成keyword.dat</div>
<dl class="notes">
<dt>参数:</dt>
<dd><code>configFilePath</code> - 配置文件路径</dd>
<dd><code>outputPath</code> - 输出文件路径</dd>
</dl>
</section>
</li>
<li>
<section class="detail" id="interactiveGenerate()">
<h3>interactiveGenerate</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">interactiveGenerate</span>()</div>
<div class="block">交互式生成配置文件</div>
</section>
</li>
<li>
<section class="detail" id="main(java.lang.String[])">
<h3>main</h3>
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">main</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/String.html" title="java.lang中的类或接口" class="external-link">String</a>[]&nbsp;args)</span></div>
<div class="block">主方法</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
