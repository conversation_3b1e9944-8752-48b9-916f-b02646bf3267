#!/bin/bash

# License Server Startup Script
# 许可证服务器启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
APP_NAME="license-server"
APP_VERSION="1.0.0"
JAR_FILE="$APP_NAME-$APP_VERSION.jar"
PID_FILE="$APP_NAME.pid"
LOG_FILE="logs/$APP_NAME.log"
CONFIG_FILE="application.yml"

# JVM参数
JVM_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:logs/gc.log"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=logs/"

# Spring配置
SPRING_OPTS="--spring.config.location=classpath:/application.yml,file:./$CONFIG_FILE"
SPRING_OPTS="$SPRING_OPTS --logging.file.name=$LOG_FILE"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  License Server 启动脚本${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        echo -e "${RED}错误: 未找到Java环境${NC}"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    echo -e "${GREEN}Java版本: $JAVA_VERSION${NC}"
}

# 检查JAR文件
check_jar() {
    if [ ! -f "$JAR_FILE" ]; then
        echo -e "${RED}错误: 未找到JAR文件 $JAR_FILE${NC}"
        exit 1
    fi
    echo -e "${GREEN}JAR文件: $JAR_FILE${NC}"
}

# 检查配置文件
check_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        echo -e "${YELLOW}警告: 未找到配置文件 $CONFIG_FILE，使用默认配置${NC}"
        SPRING_OPTS="--logging.file.name=$LOG_FILE"
    else
        echo -e "${GREEN}配置文件: $CONFIG_FILE${NC}"
    fi
}

# 创建必要目录
create_dirs() {
    mkdir -p logs
    mkdir -p data
    echo -e "${GREEN}已创建必要目录${NC}"
}

# 检查端口占用
check_port() {
    PORT=${1:-8080}
    if command -v netstat &> /dev/null; then
        if netstat -tuln | grep ":$PORT " > /dev/null; then
            echo -e "${RED}错误: 端口 $PORT 已被占用${NC}"
            exit 1
        fi
    elif command -v ss &> /dev/null; then
        if ss -tuln | grep ":$PORT " > /dev/null; then
            echo -e "${RED}错误: 端口 $PORT 已被占用${NC}"
            exit 1
        fi
    fi
    echo -e "${GREEN}端口 $PORT 可用${NC}"
}

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            return 0
        else
            rm -f $PID_FILE
            return 1
        fi
    fi
    return 1
}

# 启动服务
start_server() {
    if is_running; then
        echo -e "${YELLOW}服务已在运行中 (PID: $(cat $PID_FILE))${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}启动 License Server...${NC}"
    
    # 构建启动命令
    JAVA_CMD="java $JVM_OPTS -jar $JAR_FILE $SPRING_OPTS"
    
    # 后台启动
    nohup $JAVA_CMD > /dev/null 2>&1 &
    PID=$!
    
    # 保存PID
    echo $PID > $PID_FILE
    
    # 等待启动
    echo -e "${YELLOW}等待服务启动...${NC}"
    sleep 5
    
    # 检查启动状态
    if is_running; then
        echo -e "${GREEN}License Server 启动成功 (PID: $PID)${NC}"
        echo -e "${GREEN}日志文件: $LOG_FILE${NC}"
        
        # 等待服务完全启动
        echo -e "${YELLOW}等待服务完全启动...${NC}"
        for i in {1..30}; do
            if curl -s http://localhost:8080/api/management/health > /dev/null 2>&1; then
                echo -e "${GREEN}服务健康检查通过${NC}"
                break
            fi
            sleep 2
            echo -n "."
        done
        echo
        
    else
        echo -e "${RED}License Server 启动失败${NC}"
        if [ -f "$LOG_FILE" ]; then
            echo -e "${RED}请检查日志文件: $LOG_FILE${NC}"
            tail -20 $LOG_FILE
        fi
        exit 1
    fi
}

# 显示状态
show_status() {
    if is_running; then
        PID=$(cat $PID_FILE)
        echo -e "${GREEN}License Server 正在运行 (PID: $PID)${NC}"
        
        # 显示内存使用情况
        if command -v ps &> /dev/null; then
            MEM_USAGE=$(ps -p $PID -o pid,ppid,pcpu,pmem,vsz,rss,tty,stat,start,time,comm --no-headers)
            echo -e "${BLUE}进程信息:${NC}"
            echo "  $MEM_USAGE"
        fi
        
        # 显示端口监听情况
        if command -v netstat &> /dev/null; then
            PORTS=$(netstat -tuln | grep java || echo "无法获取端口信息")
            echo -e "${BLUE}监听端口:${NC}"
            echo "$PORTS" | grep -E ":(8080|8443)" || echo "  未找到监听端口"
        fi
        
    else
        echo -e "${RED}License Server 未运行${NC}"
    fi
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            check_java
            check_jar
            check_config
            create_dirs
            check_port 8080
            start_server
            show_status
            ;;
        status)
            show_status
            ;;
        restart)
            echo -e "${YELLOW}重启 License Server...${NC}"
            ./server-stop.sh
            sleep 3
            $0 start
            ;;
        help|--help|-h)
            echo "用法: $0 [命令]"
            echo "命令:"
            echo "  start    启动服务 (默认)"
            echo "  status   显示状态"
            echo "  restart  重启服务"
            echo "  help     显示帮助"
            ;;
        *)
            echo -e "${RED}未知命令: $1${NC}"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
