<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Mon Aug 18 14:23:24 CST 2025 -->
<title>索引 (License Client 1.0.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-08-18">
<meta name="description" content="index">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li class="nav-bar-cell1-rev">索引</li>
<li><a href="help-doc.html#index">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1>索引</h1>
</div>
<a href="#I:A">A</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:X">X</a>&nbsp;<br><a href="constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="allclasses-index.html">所有类和接口</a>
<h2 class="title" id="I:A">A</h2>
<dl class="index">
<dt><a href="com/robin/license/client/sdk/LicenseSDK.html#autoInitialize()" class="member-name-link">autoInitialize()</a> - 类中的方法 com.robin.license.client.sdk.<a href="com/robin/license/client/sdk/LicenseSDK.html" title="com.robin.license.client.sdk中的类">LicenseSDK</a></dt>
<dd>
<div class="block">自动初始化SDK
 使用加密保护的参数，无需传入明文配置</div>
</dd>
<dt><a href="com/robin/license/client/sdk/LicenseSDK.html#autoInitialize(boolean)" class="member-name-link">autoInitialize(boolean)</a> - 类中的方法 com.robin.license.client.sdk.<a href="com/robin/license/client/sdk/LicenseSDK.html" title="com.robin.license.client.sdk中的类">LicenseSDK</a></dt>
<dd>
<div class="block">自动初始化SDK
 使用加密保护的参数，无需传入明文配置</div>
</dd>
</dl>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html" class="type-name-link" title="com.robin.license.client.security中的类">CipherUtil</a> - <a href="com/robin/license/client/security/package-summary.html">com.robin.license.client.security</a>中的类</dt>
<dd>
<div class="block">加密工具类
 提供字节加密解密、配置文件生成和参数获取功能</div>
</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#%3Cinit%3E()" class="member-name-link">CipherUtil()</a> - 类的构造器 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/network/LicenseClient.html#close()" class="member-name-link">close()</a> - 类中的方法 com.robin.license.client.network.<a href="com/robin/license/client/network/LicenseClient.html" title="com.robin.license.client.network中的类">LicenseClient</a></dt>
<dd>
<div class="block">关闭HTTP客户端</div>
</dd>
<dt><a href="com/robin/license/client/hardware/MachineInfoCollector.html#collectMachineInfo()" class="member-name-link">collectMachineInfo()</a> - 类中的静态方法 com.robin.license.client.hardware.<a href="com/robin/license/client/hardware/MachineInfoCollector.html" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></dt>
<dd>
<div class="block">收集完整的机器信息</div>
</dd>
<dt><a href="com/robin/license/client/core/package-summary.html">com.robin.license.client.core</a> - 程序包 com.robin.license.client.core</dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/example/package-summary.html">com.robin.license.client.example</a> - 程序包 com.robin.license.client.example</dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/hardware/package-summary.html">com.robin.license.client.hardware</a> - 程序包 com.robin.license.client.hardware</dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/network/package-summary.html">com.robin.license.client.network</a> - 程序包 com.robin.license.client.network</dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/sdk/package-summary.html">com.robin.license.client.sdk</a> - 程序包 com.robin.license.client.sdk</dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/security/package-summary.html">com.robin.license.client.security</a> - 程序包 com.robin.license.client.security</dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:D">D</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html#decryptByte(byte%5B%5D,byte%5B%5D)" class="member-name-link">decryptByte(byte[], byte[])</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">解密字节数组</div>
</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#DEFAULTKEY" class="member-name-link">DEFAULTKEY</a> - 类中的静态变量 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">默认加密密钥</div>
</dd>
</dl>
<h2 class="title" id="I:E">E</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html#encryptByte(byte%5B%5D,byte%5B%5D)" class="member-name-link">encryptByte(byte[], byte[])</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">加密字节数组</div>
</dd>
</dl>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html#generateFromProperties(java.lang.String,java.lang.String)" class="member-name-link">generateFromProperties(String, String)</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">从properties文件生成keyword.dat</div>
</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#generateKeywordFile(java.lang.String,java.util.Map)" class="member-name-link">generateKeywordFile(String, Map&lt;Integer, String&gt;)</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">从配置映射生成keyword.dat文件</div>
</dd>
<dt><a href="com/robin/license/client/hardware/MachineInfoCollector.html#getCpuSerial()" class="member-name-link">getCpuSerial()</a> - 类中的静态方法 com.robin.license.client.hardware.<a href="com/robin/license/client/hardware/MachineInfoCollector.html" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></dt>
<dd>
<div class="block">获取CPU序列号</div>
</dd>
<dt><a href="com/robin/license/client/hardware/MachineInfoCollector.html#getDiskSerial()" class="member-name-link">getDiskSerial()</a> - 类中的静态方法 com.robin.license.client.hardware.<a href="com/robin/license/client/hardware/MachineInfoCollector.html" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></dt>
<dd>
<div class="block">获取硬盘序列号</div>
</dd>
<dt><a href="com/robin/license/client/sdk/LicenseSDK.html#getInstance()" class="member-name-link">getInstance()</a> - 类中的静态方法 com.robin.license.client.sdk.<a href="com/robin/license/client/sdk/LicenseSDK.html" title="com.robin.license.client.sdk中的类">LicenseSDK</a></dt>
<dd>
<div class="block">获取SDK实例</div>
</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#getInstance()" class="member-name-link">getInstance()</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">获取单例实例</div>
</dd>
<dt><a href="com/robin/license/client/sdk/LicenseSDK.html#getLicenseInfo()" class="member-name-link">getLicenseInfo()</a> - 类中的方法 com.robin.license.client.sdk.<a href="com/robin/license/client/sdk/LicenseSDK.html" title="com.robin.license.client.sdk中的类">LicenseSDK</a></dt>
<dd>
<div class="block">获取许可证信息</div>
</dd>
<dt><a href="com/robin/license/client/hardware/MachineInfoCollector.html#getMachineId()" class="member-name-link">getMachineId()</a> - 类中的静态方法 com.robin.license.client.hardware.<a href="com/robin/license/client/hardware/MachineInfoCollector.html" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></dt>
<dd>
<div class="block">获取机器唯一标识</div>
</dd>
<dt><a href="com/robin/license/client/hardware/MachineInfoCollector.html#getMotherboardSerial()" class="member-name-link">getMotherboardSerial()</a> - 类中的静态方法 com.robin.license.client.hardware.<a href="com/robin/license/client/hardware/MachineInfoCollector.html" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></dt>
<dd>
<div class="block">获取主板序列号</div>
</dd>
</dl>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html#interactiveGenerate()" class="member-name-link">interactiveGenerate()</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">交互式生成配置文件</div>
</dd>
<dt><a href="com/robin/license/client/sdk/LicenseSDK.html#isLicenseValid()" class="member-name-link">isLicenseValid()</a> - 类中的方法 com.robin.license.client.sdk.<a href="com/robin/license/client/sdk/LicenseSDK.html" title="com.robin.license.client.sdk中的类">LicenseSDK</a></dt>
<dd>
<div class="block">检查许可证是否有效</div>
</dd>
</dl>
<h2 class="title" id="I:L">L</h2>
<dl class="index">
<dt><a href="com/robin/license/client/network/LicenseClient.html" class="type-name-link" title="com.robin.license.client.network中的类">LicenseClient</a> - <a href="com/robin/license/client/network/package-summary.html">com.robin.license.client.network</a>中的类</dt>
<dd>
<div class="block">许可证网络客户端
 负责与许可证服务器的网络通信</div>
</dd>
<dt><a href="com/robin/license/client/network/LicenseClient.html#%3Cinit%3E(java.lang.String)" class="member-name-link">LicenseClient(String)</a> - 类的构造器 com.robin.license.client.network.<a href="com/robin/license/client/network/LicenseClient.html" title="com.robin.license.client.network中的类">LicenseClient</a></dt>
<dd>
<div class="block">构造函数</div>
</dd>
<dt><a href="com/robin/license/client/sdk/LicenseSDK.html" class="type-name-link" title="com.robin.license.client.sdk中的类">LicenseSDK</a> - <a href="com/robin/license/client/sdk/package-summary.html">com.robin.license.client.sdk</a>中的类</dt>
<dd>
<div class="block">许可证SDK自动化入口类
 提供自动初始化、机器注册、定时验证等功能</div>
</dd>
<dt><a href="com/robin/license/client/core/LicenseValidator.html" class="type-name-link" title="com.robin.license.client.core中的类">LicenseValidator</a> - <a href="com/robin/license/client/core/package-summary.html">com.robin.license.client.core</a>中的类</dt>
<dd>
<div class="block">许可证验证器
 负责本地许可证文件的验证逻辑</div>
</dd>
<dt><a href="com/robin/license/client/core/LicenseValidator.html#%3Cinit%3E()" class="member-name-link">LicenseValidator()</a> - 类的构造器 com.robin.license.client.core.<a href="com/robin/license/client/core/LicenseValidator.html" title="com.robin.license.client.core中的类">LicenseValidator</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:M">M</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html#m_datapadding" class="member-name-link">m_datapadding</a> - 类中的静态变量 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">数据填充字节，用于分隔数据项</div>
</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#m_ending" class="member-name-link">m_ending</a> - 类中的静态变量 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">文件结束标记</div>
</dd>
<dt><a href="com/robin/license/client/hardware/MachineInfoCollector.html" class="type-name-link" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a> - <a href="com/robin/license/client/hardware/package-summary.html">com.robin.license.client.hardware</a>中的类</dt>
<dd>
<div class="block">机器信息收集器
 跨平台收集机器硬件信息用于生成唯一标识</div>
</dd>
<dt><a href="com/robin/license/client/hardware/MachineInfoCollector.html#%3Cinit%3E()" class="member-name-link">MachineInfoCollector()</a> - 类的构造器 com.robin.license.client.hardware.<a href="com/robin/license/client/hardware/MachineInfoCollector.html" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/example/SimpleSDKExample.html#main(java.lang.String%5B%5D)" class="member-name-link">main(String[])</a> - 类中的静态方法 com.robin.license.client.example.<a href="com/robin/license/client/example/SimpleSDKExample.html" title="com.robin.license.client.example中的类">SimpleSDKExample</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#main(java.lang.String%5B%5D)" class="member-name-link">main(String[])</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">主方法</div>
</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#mzHeader" class="member-name-link">mzHeader</a> - 类中的静态变量 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">EXE文件头标识，用于伪装成exe文件</div>
</dd>
</dl>
<h2 class="title" id="I:R">R</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html#retKeyword(java.lang.Integer)" class="member-name-link">retKeyword(Integer)</a> - 类中的方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">根据数字ID获取关键字值</div>
</dd>
</dl>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="com/robin/license/client/sdk/LicenseSDK.html#shutdown()" class="member-name-link">shutdown()</a> - 类中的方法 com.robin.license.client.sdk.<a href="com/robin/license/client/sdk/LicenseSDK.html" title="com.robin.license.client.sdk中的类">LicenseSDK</a></dt>
<dd>
<div class="block">关闭SDK</div>
</dd>
<dt><a href="com/robin/license/client/example/SimpleSDKExample.html" class="type-name-link" title="com.robin.license.client.example中的类">SimpleSDKExample</a> - <a href="com/robin/license/client/example/package-summary.html">com.robin.license.client.example</a>中的类</dt>
<dd>
<div class="block">简单SDK使用示例
 展示最简单的集成方式</div>
</dd>
<dt><a href="com/robin/license/client/example/SimpleSDKExample.html#%3Cinit%3E()" class="member-name-link">SimpleSDKExample()</a> - 类的构造器 com.robin.license.client.example.<a href="com/robin/license/client/example/SimpleSDKExample.html" title="com.robin.license.client.example中的类">SimpleSDKExample</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/robin/license/client/network/LicenseClient.html#syncLicense(com.robin.license.common.dto.MachineInfo)" class="member-name-link">syncLicense(MachineInfo)</a> - 类中的方法 com.robin.license.client.network.<a href="com/robin/license/client/network/LicenseClient.html" title="com.robin.license.client.network中的类">LicenseClient</a></dt>
<dd>
<div class="block">同步许可证（统一的注册和验证接口）</div>
</dd>
</dl>
<h2 class="title" id="I:V">V</h2>
<dl class="index">
<dt><a href="com/robin/license/client/core/LicenseValidator.html#validateLicense()" class="member-name-link">validateLicense()</a> - 类中的静态方法 com.robin.license.client.core.<a href="com/robin/license/client/core/LicenseValidator.html" title="com.robin.license.client.core中的类">LicenseValidator</a></dt>
<dd>
<div class="block">验证许可证</div>
</dd>
<dt><a href="com/robin/license/client/network/LicenseClient.html#verifyLicense(com.robin.license.common.dto.LicenseInfo)" class="member-name-link">verifyLicense(LicenseInfo)</a> - 类中的方法 com.robin.license.client.network.<a href="com/robin/license/client/network/LicenseClient.html" title="com.robin.license.client.network中的类">LicenseClient</a></dt>
<dd>
<div class="block">验证许可证</div>
</dd>
</dl>
<h2 class="title" id="I:X">X</h2>
<dl class="index">
<dt><a href="com/robin/license/client/security/CipherUtil.html#xorDecrypt(byte%5B%5D,byte%5B%5D)" class="member-name-link">xorDecrypt(byte[], byte[])</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">简单的XOR解密（作为备用方案）</div>
</dd>
<dt><a href="com/robin/license/client/security/CipherUtil.html#xorEncrypt(byte%5B%5D,byte%5B%5D)" class="member-name-link">xorEncrypt(byte[], byte[])</a> - 类中的静态方法 com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></dt>
<dd>
<div class="block">简单的XOR加密（作为备用方案）</div>
</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:V">V</a>&nbsp;<a href="#I:X">X</a>&nbsp;<br><a href="constant-values.html">常量字段值</a><span class="vertical-separator">|</span><a href="allpackages-index.html">所有程序包</a><span class="vertical-separator">|</span><a href="allclasses-index.html">所有类和接口</a></main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
