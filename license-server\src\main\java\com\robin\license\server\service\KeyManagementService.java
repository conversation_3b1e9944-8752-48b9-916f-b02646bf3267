package com.robin.license.server.service;


import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.crypto.CryptoUtil;
import com.robin.license.server.entity.ClientKeyPair;
import com.robin.license.server.repository.ClientKeyPairRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.*;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 密钥管理服务
 * 负责RSA密钥对的生成、存储、加载和管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Slf4j
public class KeyManagementService {

    @Autowired
    private ClientKeyPairRepository clientKeyPairRepository;
    
    @Value("${license.key.private-key-path:#{null}}")
    private String privateKeyPath;
    
    @Value("${license.key.public-key-path:#{null}}")
    private String publicKeyPath;
    
    @Value("${license.key.key-size:2048}")
    private int keySize;
    
    @Value("${license.key.auto-generate:true}")
    private boolean autoGenerate;
    
    private PrivateKey privateKey;
    private PublicKey publicKey;
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    // 客户端特定的密钥对缓存
    private final ConcurrentHashMap<String, KeyPair> clientKeyPairs = new ConcurrentHashMap<>();
    
    /**
     * 初始化密钥管理服务
     */
    @PostConstruct
    public void initialize() {
        log.info("初始化密钥管理服务...");
        
        try {
            // 设置默认路径
            if (privateKeyPath == null) {
                privateKeyPath = System.getProperty("user.home") + File.separator +
                                 LicenseConstants.File.LICENSE_DIR + File.separator +
                                 LicenseConstants.File.PRIVATE_KEY_FILE;
            }
            
            if (publicKeyPath == null) {
                publicKeyPath = System.getProperty("user.home") + File.separator +
                              LicenseConstants.File.LICENSE_DIR + File.separator +
                              LicenseConstants.File.SERVER_PUBLIC_KEY_FILE;
            }
            
            // 加载或生成密钥对
            loadOrGenerateKeyPair();
            
            log.info("密钥管理服务初始化完成");
            log.info("私钥路径: {}", privateKeyPath);
            log.info("公钥路径: {}", publicKeyPath);
            
        } catch (Exception e) {
            log.error("密钥管理服务初始化失败", e);
            throw new RuntimeException("密钥管理服务初始化失败", e);
        }
    }
    
    /**
     * 获取私钥
     * 
     * @return 私钥
     */
    public PrivateKey getPrivateKey() {
        lock.readLock().lock();
        try {
            if (privateKey == null) {
                throw new IllegalStateException("私钥未初始化");
            }
            return privateKey;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 获取公钥
     * 
     * @return 公钥
     */
    public PublicKey getPublicKey() {
        lock.readLock().lock();
        try {
            if (publicKey == null) {
                throw new IllegalStateException("公钥未初始化");
            }
            return publicKey;
        } finally {
            lock.readLock().unlock();
        }
    }
    
    /**
     * 获取公钥PEM格式字符串
     * 
     * @return 公钥PEM字符串
     */
    public String getPublicKeyPem() {
        return CryptoUtil.writePublicKeyToPem(getPublicKey());
    }
    
    /**
     * 获取私钥PEM格式字符串
     * 
     * @return 私钥PEM字符串
     */
    public String getPrivateKeyPem() {
        return CryptoUtil.writePrivateKeyToPem(getPrivateKey());
    }
    
    /**
     * 重新生成密钥对
     * 
     * @return 是否成功
     */
    public boolean regenerateKeyPair() {
        lock.writeLock().lock();
        try {
            log.info("重新生成密钥对...");
            
            // 备份现有密钥文件
            backupExistingKeys();
            
            // 生成新的密钥对
            KeyPair keyPair = CryptoUtil.generateRSAKeyPair(keySize);
            
            // 保存密钥对
            saveKeyPair(keyPair);
            
            // 更新内存中的密钥
            this.privateKey = keyPair.getPrivate();
            this.publicKey = keyPair.getPublic();
            
            log.info("密钥对重新生成完成");
            return true;
            
        } catch (Exception e) {
            log.error("重新生成密钥对失败", e);
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }
    
    /**
     * 验证密钥对是否匹配
     * 
     * @return 是否匹配
     */
    public boolean validateKeyPair() {
        try {
            // 使用测试数据验证密钥对
            String testData = "KeyPair Validation Test";
            byte[] signature = CryptoUtil.signRSA(testData.getBytes(), getPrivateKey());
            return CryptoUtil.verifyRSA(testData.getBytes(), signature, getPublicKey());
        } catch (Exception e) {
            log.error("验证密钥对失败", e);
            return false;
        }
    }
    
    /**
     * 加载或生成密钥对
     */
    private void loadOrGenerateKeyPair() {
        // 尝试加载现有密钥
        if (loadExistingKeyPair()) {
            log.info("成功加载现有密钥对");
            
            // 验证密钥对
            if (validateKeyPair()) {
                log.info("密钥对验证通过");
                return;
            } else {
                log.warn("密钥对验证失败，将重新生成");
            }
        }
        
        // 生成新的密钥对
        if (autoGenerate) {
            generateNewKeyPair();
        } else {
            throw new RuntimeException("无法加载密钥对，且自动生成已禁用");
        }
    }
    
    /**
     * 加载现有密钥对
     * 
     * @return 是否成功加载
     */
    private boolean loadExistingKeyPair() {
        try {
            File privateKeyFile = new File(privateKeyPath);
            File publicKeyFile = new File(publicKeyPath);
            
            if (!privateKeyFile.exists() || !publicKeyFile.exists()) {
                log.info("密钥文件不存在，需要生成新的密钥对");
                return false;
            }
            
            // 读取私钥
            String privateKeyPem = readFileContent(privateKeyFile);
            this.privateKey = CryptoUtil.readPrivateKeyFromPem(privateKeyPem);
            
            // 读取公钥
            String publicKeyPem = readFileContent(publicKeyFile);
            this.publicKey = CryptoUtil.readPublicKeyFromPem(publicKeyPem);
            
            return true;
            
        } catch (Exception e) {
            log.error("加载现有密钥对失败", e);
            return false;
        }
    }
    
    /**
     * 生成新的密钥对
     */
    private void generateNewKeyPair() {
        try {
            log.info("生成新的密钥对，密钥长度: {}", keySize);
            
            // 生成密钥对
            KeyPair keyPair = CryptoUtil.generateRSAKeyPair(keySize);
            
            // 保存密钥对
            saveKeyPair(keyPair);
            
            // 设置内存中的密钥
            this.privateKey = keyPair.getPrivate();
            this.publicKey = keyPair.getPublic();
            
            log.info("新密钥对生成完成");
            
        } catch (Exception e) {
            log.error("生成新密钥对失败", e);
            throw new RuntimeException("生成新密钥对失败", e);
        }
    }
    
    /**
     * 保存密钥对到文件
     * 
     * @param keyPair 密钥对
     */
    private void saveKeyPair(KeyPair keyPair) throws IOException {
        // 确保目录存在
        File privateKeyFile = new File(privateKeyPath);
        File publicKeyFile = new File(publicKeyPath);
        
        privateKeyFile.getParentFile().mkdirs();
        publicKeyFile.getParentFile().mkdirs();
        
        // 保存私钥
        String privateKeyPem = CryptoUtil.writePrivateKeyToPem(keyPair.getPrivate());
        writeFileContent(privateKeyFile, privateKeyPem);
        
        // 保存公钥
        String publicKeyPem = CryptoUtil.writePublicKeyToPem(keyPair.getPublic());
        writeFileContent(publicKeyFile, publicKeyPem);
        
        // 设置文件权限（仅所有者可读写）
        privateKeyFile.setReadable(false, false);
        privateKeyFile.setReadable(true, true);
        privateKeyFile.setWritable(false, false);
        privateKeyFile.setWritable(true, true);
        
        log.info("密钥对已保存到文件");
    }
    
    /**
     * 备份现有密钥文件
     */
    private void backupExistingKeys() {
        try {
            File privateKeyFile = new File(privateKeyPath);
            File publicKeyFile = new File(publicKeyPath);
            
            if (privateKeyFile.exists()) {
                File backupFile = new File(privateKeyPath + LicenseConstants.File.BACKUP_SUFFIX);
                copyFile(privateKeyFile, backupFile);
                log.info("私钥文件已备份: {}", backupFile.getAbsolutePath());
            }
            
            if (publicKeyFile.exists()) {
                File backupFile = new File(publicKeyPath + LicenseConstants.File.BACKUP_SUFFIX);
                copyFile(publicKeyFile, backupFile);
                log.info("公钥文件已备份: {}", backupFile.getAbsolutePath());
            }
            
        } catch (Exception e) {
            log.warn("备份密钥文件失败", e);
        }
    }
    
    /**
     * 读取文件内容
     * 
     * @param file 文件
     * @return 文件内容
     */
    private String readFileContent(File file) throws IOException {
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }
        }
        return content.toString();
    }
    
    /**
     * 写入文件内容
     * 
     * @param file 文件
     * @param content 内容
     */
    private void writeFileContent(File file, String content) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(file))) {
            writer.write(content);
        }
    }
    
    /**
     * 复制文件
     * 
     * @param source 源文件
     * @param target 目标文件
     */
    private void copyFile(File source, File target) throws IOException {
        try (InputStream is = new FileInputStream(source);
             OutputStream os = new FileOutputStream(target)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = is.read(buffer)) > 0) {
                os.write(buffer, 0, length);
            }
        }
    }

    /**
     * 获取客户端特定的密钥对
     * 如果不存在则生成新的密钥对
     *
     * @param clientId 客户端ID（通常是机器指纹）
     * @return 客户端密钥对
     */
    public KeyPair getClientKeyPair(String clientId) {
        // 先从缓存中查找
        KeyPair cachedKeyPair = clientKeyPairs.get(clientId);
        if (cachedKeyPair != null) {
            return cachedKeyPair;
        }

        // 从数据库中查找
        Optional<ClientKeyPair> clientKeyPairOpt = clientKeyPairRepository.findValidByClientId(clientId);
        if (clientKeyPairOpt.isPresent()) {
            ClientKeyPair clientKeyPairEntity = clientKeyPairOpt.get();
            try {
                // 从PEM格式恢复密钥对
                PrivateKey privateKey = CryptoUtil.readPrivateKeyFromPem(clientKeyPairEntity.getPrivateKeyPem());
                PublicKey publicKey = CryptoUtil.readPublicKeyFromPem(clientKeyPairEntity.getPublicKeyPem());
                KeyPair keyPair = new KeyPair(publicKey, privateKey);

                // 缓存到内存
                clientKeyPairs.put(clientId, keyPair);

                // 更新最后使用时间
                clientKeyPairEntity.updateLastUsedTime();
                clientKeyPairRepository.save(clientKeyPairEntity);

                return keyPair;
            } catch (Exception e) {
                log.error("从数据库恢复客户端密钥对失败: " + clientId, e);
            }
        }

        // 生成新的密钥对
        return generateAndSaveClientKeyPair(clientId);
    }

    /**
     * 获取客户端的私钥
     *
     * @param clientId 客户端ID
     * @return 私钥
     */
    public PrivateKey getClientPrivateKey(String clientId) {
        return getClientKeyPair(clientId).getPrivate();
    }

    /**
     * 获取客户端的公钥
     *
     * @param clientId 客户端ID
     * @return 公钥
     */
    public PublicKey getClientPublicKey(String clientId) {
        return getClientKeyPair(clientId).getPublic();
    }

    /**
     * 获取客户端公钥的PEM格式字符串
     *
     * @param clientId 客户端ID
     * @return 公钥PEM字符串
     */
    public String getClientPublicKeyPem(String clientId) {
        return CryptoUtil.writePublicKeyToPem(getClientPublicKey(clientId));
    }

    /**
     * 为客户端生成新的密钥对并保存到数据库
     *
     * @param clientId 客户端ID
     * @return 新生成的密钥对
     */
    private KeyPair generateAndSaveClientKeyPair(String clientId) {
        try {
            log.info("为客户端生成新密钥对: {}", clientId);
            KeyPair keyPair = CryptoUtil.generateRSAKeyPair(keySize);

            // 转换为PEM格式
            String privateKeyPem = CryptoUtil.writePrivateKeyToPem(keyPair.getPrivate());
            String publicKeyPem = CryptoUtil.writePublicKeyToPem(keyPair.getPublic());

            // 保存到数据库
            ClientKeyPair clientKeyPairEntity = ClientKeyPair.builder()
                    .clientId(clientId)
                    .privateKeyPem(privateKeyPem)
                    .publicKeyPem(publicKeyPem)
                    .keySize(keySize)
                    .build();

            clientKeyPairRepository.save(clientKeyPairEntity);

            // 缓存到内存
            clientKeyPairs.put(clientId, keyPair);

            log.info("客户端密钥对生成并保存完成: {}", clientId);
            return keyPair;

        } catch (Exception e) {
            log.error("为客户端生成密钥对失败: " + clientId, e);
            throw new RuntimeException("生成客户端密钥对失败", e);
        }
    }



    /**
     * 撤销客户端密钥对
     *
     * @param clientId 客户端ID
     */
    public void revokeClientKeyPair(String clientId) {
        // 从缓存中移除
        clientKeyPairs.remove(clientId);

        // 在数据库中标记为撤销
        Optional<ClientKeyPair> clientKeyPairOpt = clientKeyPairRepository.findByClientId(clientId);
        if (clientKeyPairOpt.isPresent()) {
            ClientKeyPair clientKeyPairEntity = clientKeyPairOpt.get();
            clientKeyPairEntity.revoke();
            clientKeyPairRepository.save(clientKeyPairEntity);
        }

        log.info("客户端密钥对已撤销: {}", clientId);
    }
}
