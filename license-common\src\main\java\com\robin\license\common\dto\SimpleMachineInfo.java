package com.robin.license.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

/**
 * 简化的机器信息数据传输对象
 * 只包含网络传输必需的字段
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SimpleMachineInfo {
    
    /**
     * 机器唯一标识
     */
    private String machineId;
    
    /**
     * CPU序列号
     */
    private String cpuSerial;
    
    /**
     * 主板序列号
     */
    private String motherboardSerial;
    
    /**
     * 硬盘序列号
     */
    private String diskSerial;
    
    /**
     * 机器指纹（综合标识）
     */
    private String fingerprint;
    
    /**
     * 生成机器指纹
     * 基于多个硬件标识生成唯一指纹
     * 
     * @return 机器指纹
     */
    public String generateFingerprint() {
        StringBuilder builder = new StringBuilder();
        
        if (machineId != null) {
            builder.append(machineId);
        }
        if (cpuSerial != null) {
            builder.append(cpuSerial);
        }
        if (motherboardSerial != null) {
            builder.append(motherboardSerial);
        }
        if (diskSerial != null) {
            builder.append(diskSerial);
        }
        
        // 移除特殊字符
        String raw = builder.toString().replaceAll("[\\-\\s]", "");
        
        // 生成MD5哈希作为指纹
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(raw.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            this.fingerprint = hexString.toString().toUpperCase();
            return this.fingerprint;
        } catch (Exception e) {
            // 如果哈希失败，直接返回原始字符串
            this.fingerprint = raw;
            return this.fingerprint;
        }
    }
    
    /**
     * 验证机器指纹是否匹配
     * 
     * @param targetFingerprint 目标指纹
     * @return 是否匹配
     */
    public boolean verifyFingerprint(String targetFingerprint) {
        if (targetFingerprint == null || targetFingerprint.trim().isEmpty()) {
            return false;
        }
        
        String currentFingerprint = this.fingerprint;
        if (currentFingerprint == null) {
            currentFingerprint = generateFingerprint();
        }
        
        return targetFingerprint.equalsIgnoreCase(currentFingerprint);
    }
    
    /**
     * 从完整的MachineInfo创建SimpleMachineInfo
     * 
     * @param machineInfo 完整的机器信息
     * @return 简化的机器信息
     */
    public static SimpleMachineInfo fromMachineInfo(MachineInfo machineInfo) {
        return SimpleMachineInfo.builder()
                .machineId(machineInfo.getMachineId())
                .cpuSerial(machineInfo.getCpuSerial())
                .motherboardSerial(machineInfo.getMotherboardSerial())
                .diskSerial(machineInfo.getDiskSerial())
                .fingerprint(machineInfo.getFingerprint())
                .build();
    }
}
