<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Mon Aug 18 14:23:24 CST 2025 -->
<title>所有类和接口 (License Client 1.0.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-08-18">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li><a href="overview-tree.html">树</a></li>
<li><a href="index-all.html">索引</a></li>
<li><a href="help-doc.html#all-classes">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 title="所有类和接口" class="title">所有类和接口</h1>
</div>
<div id="all-classes-table">
<div class="caption"><span>类</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">类</div>
<div class="table-header col-last">说明</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/robin/license/client/security/CipherUtil.html" title="com.robin.license.client.security中的类">CipherUtil</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">加密工具类
 提供字节加密解密、配置文件生成和参数获取功能</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/robin/license/client/network/LicenseClient.html" title="com.robin.license.client.network中的类">LicenseClient</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">许可证网络客户端
 负责与许可证服务器的网络通信</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/robin/license/client/sdk/LicenseSDK.html" title="com.robin.license.client.sdk中的类">LicenseSDK</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">许可证SDK自动化入口类
 提供自动初始化、机器注册、定时验证等功能</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/robin/license/client/core/LicenseValidator.html" title="com.robin.license.client.core中的类">LicenseValidator</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">许可证验证器
 负责本地许可证文件的验证逻辑</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/robin/license/client/hardware/MachineInfoCollector.html" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">机器信息收集器
 跨平台收集机器硬件信息用于生成唯一标识</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/robin/license/client/example/SimpleSDKExample.html" title="com.robin.license.client.example中的类">SimpleSDKExample</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">简单SDK使用示例
 展示最简单的集成方式</div>
</div>
</div>
</div>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
