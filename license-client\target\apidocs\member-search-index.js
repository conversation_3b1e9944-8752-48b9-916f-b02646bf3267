memberSearchIndex = [{"p":"com.robin.license.client.sdk","c":"LicenseSDK","l":"autoInitialize()"},{"p":"com.robin.license.client.sdk","c":"LicenseSDK","l":"autoInitialize(boolean)"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"CipherUtil()","u":"%3Cinit%3E()"},{"p":"com.robin.license.client.network","c":"LicenseClient","l":"close()"},{"p":"com.robin.license.client.hardware","c":"MachineInfoCollector","l":"collectMachineInfo()"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"decryptByte(byte[], byte[])","u":"decryptByte(byte[],byte[])"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"DEFAULTKEY"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"encryptByte(byte[], byte[])","u":"encryptByte(byte[],byte[])"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"generateFromProperties(String, String)","u":"generateFromProperties(java.lang.String,java.lang.String)"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"generateKeywordFile(String, Map<Integer, String>)","u":"generateKeywordFile(java.lang.String,java.util.Map)"},{"p":"com.robin.license.client.hardware","c":"MachineInfoCollector","l":"getCpuSerial()"},{"p":"com.robin.license.client.hardware","c":"MachineInfoCollector","l":"getDiskSerial()"},{"p":"com.robin.license.client.sdk","c":"LicenseSDK","l":"getInstance()"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"getInstance()"},{"p":"com.robin.license.client.sdk","c":"LicenseSDK","l":"getLicenseInfo()"},{"p":"com.robin.license.client.hardware","c":"MachineInfoCollector","l":"getMachineId()"},{"p":"com.robin.license.client.hardware","c":"MachineInfoCollector","l":"getMotherboardSerial()"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"interactiveGenerate()"},{"p":"com.robin.license.client.sdk","c":"LicenseSDK","l":"isLicenseValid()"},{"p":"com.robin.license.client.network","c":"LicenseClient","l":"LicenseClient(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.robin.license.client.core","c":"LicenseValidator","l":"LicenseValidator()","u":"%3Cinit%3E()"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"m_datapadding"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"m_ending"},{"p":"com.robin.license.client.hardware","c":"MachineInfoCollector","l":"MachineInfoCollector()","u":"%3Cinit%3E()"},{"p":"com.robin.license.client.example","c":"SimpleSDKExample","l":"main(String[])","u":"main(java.lang.String[])"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"main(String[])","u":"main(java.lang.String[])"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"mzHeader"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"retKeyword(Integer)","u":"retKeyword(java.lang.Integer)"},{"p":"com.robin.license.client.sdk","c":"LicenseSDK","l":"shutdown()"},{"p":"com.robin.license.client.example","c":"SimpleSDKExample","l":"SimpleSDKExample()","u":"%3Cinit%3E()"},{"p":"com.robin.license.client.network","c":"LicenseClient","l":"syncLicense(MachineInfo)","u":"syncLicense(com.robin.license.common.dto.MachineInfo)"},{"p":"com.robin.license.client.core","c":"LicenseValidator","l":"validateLicense()"},{"p":"com.robin.license.client.network","c":"LicenseClient","l":"verifyLicense(LicenseInfo)","u":"verifyLicense(com.robin.license.common.dto.LicenseInfo)"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"xorDecrypt(byte[], byte[])","u":"xorDecrypt(byte[],byte[])"},{"p":"com.robin.license.client.security","c":"CipherUtil","l":"xorEncrypt(byte[], byte[])","u":"xorEncrypt(byte[],byte[])"}];updateSearchResults();