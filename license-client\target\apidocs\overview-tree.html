<!DOCTYPE HTML>
<html lang="zh">
<head>
<!-- Generated by javadoc (21) on Mon Aug 18 14:23:24 CST 2025 -->
<title>类分层结构 (License Client 1.0.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-08-18">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="stylesheet.css" title="Style">
<link rel="stylesheet" type="text/css" href="script-dir/jquery-ui.min.css" title="Style">
<script type="text/javascript" src="script.js"></script>
<script type="text/javascript" src="script-dir/jquery-3.6.1.min.js"></script>
<script type="text/javascript" src="script-dir/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">var pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>您的浏览器已禁用 JavaScript。</div>
</noscript>
<div class="flex-box">
<header role="banner" class="flex-header">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="切换导航链接"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button>
<div class="skip-nav"><a href="#skip-navbar-top" title="跳过导航链接">跳过导航链接</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="导航">
<li><a href="index.html">概览</a></li>
<li>程序包</li>
<li>类</li>
<li>使用</li>
<li class="nav-bar-cell1-rev">树</li>
<li><a href="index-all.html">索引</a></li>
<li><a href="help-doc.html#tree">帮助</a></li>
</ul>
</div>
<div class="sub-nav">
<div id="navbar-sub-list"></div>
<div class="nav-list-search"><a href="search.html">SEARCH</a>
<input type="text" id="search-input" disabled placeholder="搜索">
<input type="reset" id="reset-button" disabled value="reset">
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="flex-content">
<main role="main">
<div class="header">
<h1 class="title">所有程序包的分层结构</h1>
</div>
<span class="package-hierarchy-label">程序包分层结构:</span>
<ul class="horizontal contents-list">
<li><a href="com/robin/license/client/core/package-tree.html">com.robin.license.client.core</a>, </li>
<li><a href="com/robin/license/client/example/package-tree.html">com.robin.license.client.example</a>, </li>
<li><a href="com/robin/license/client/hardware/package-tree.html">com.robin.license.client.hardware</a>, </li>
<li><a href="com/robin/license/client/network/package-tree.html">com.robin.license.client.network</a>, </li>
<li><a href="com/robin/license/client/sdk/package-tree.html">com.robin.license.client.sdk</a>, </li>
<li><a href="com/robin/license/client/security/package-tree.html">com.robin.license.client.security</a></li>
</ul>
<section class="hierarchy">
<h2 title="类分层结构">类分层结构</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="java.lang中的类或接口">Object</a>
<ul>
<li class="circle">com.robin.license.client.security.<a href="com/robin/license/client/security/CipherUtil.html" class="type-name-link" title="com.robin.license.client.security中的类">CipherUtil</a></li>
<li class="circle">com.robin.license.client.network.<a href="com/robin/license/client/network/LicenseClient.html" class="type-name-link" title="com.robin.license.client.network中的类">LicenseClient</a></li>
<li class="circle">com.robin.license.client.sdk.<a href="com/robin/license/client/sdk/LicenseSDK.html" class="type-name-link" title="com.robin.license.client.sdk中的类">LicenseSDK</a></li>
<li class="circle">com.robin.license.client.core.<a href="com/robin/license/client/core/LicenseValidator.html" class="type-name-link" title="com.robin.license.client.core中的类">LicenseValidator</a></li>
<li class="circle">com.robin.license.client.hardware.<a href="com/robin/license/client/hardware/MachineInfoCollector.html" class="type-name-link" title="com.robin.license.client.hardware中的类">MachineInfoCollector</a></li>
<li class="circle">com.robin.license.client.example.<a href="com/robin/license/client/example/SimpleSDKExample.html" class="type-name-link" title="com.robin.license.client.example中的类">SimpleSDKExample</a></li>
</ul>
</li>
</ul>
</section>
</main>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</div>
</div>
</body>
</html>
