@options
@packages
-classpath
'C:/Users/<USER>/IdeaProjects/study/license-system/license-common/target/license-common-1.0.0.jar;C:/Java/Maven/warehouse/commons-codec/commons-codec/1.15/commons-codec-1.15.jar;C:/Java/Maven/warehouse/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar;C:/Java/Maven/warehouse/org/bouncycastle/bcpkix-jdk15on/1.70/bcpkix-jdk15on-1.70.jar;C:/Java/Maven/warehouse/org/bouncycastle/bcutil-jdk15on/1.70/bcutil-jdk15on-1.70.jar;C:/Java/Maven/warehouse/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar;C:/Java/Maven/warehouse/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar;C:/Java/Maven/warehouse/commons-logging/commons-logging/1.2/commons-logging-1.2.jar;C:/Java/Maven/warehouse/cn/hutool/hutool-all/5.8.22/hutool-all-5.8.22.jar;C:/Java/Maven/warehouse/com/google/code/gson/gson/2.10.1/gson-2.10.1.jar;C:/Java/Maven/warehouse/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar;C:/Java/Maven/warehouse/org/projectlombok/lombok/1.18.30/lombok-1.18.30.jar;C:/Java/Maven/warehouse/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar;C:/Java/Maven/warehouse/ch/qos/logback/logback-classic/1.2.12/logback-classic-1.2.12.jar;C:/Java/Maven/warehouse/ch/qos/logback/logback-core/1.2.12/logback-core-1.2.12.jar;C:/Java/Maven/warehouse/org/springframework/spring-core/5.3.31/spring-core-5.3.31.jar;C:/Java/Maven/warehouse/org/springframework/spring-jcl/5.3.31/spring-jcl-5.3.31.jar'
-encoding
'UTF-8'
-protected
-quiet
-source
'11'
-sourcepath
'C:/Users/<USER>/IdeaProjects/study/license-system/license-client/src/main/java;C:/Users/<USER>/IdeaProjects/study/license-system/license-client/target/generated-sources/annotations'
-author
-bottom
'Copyright &#169; 2025. All rights reserved.'
-charset
'UTF-8'
-d
'C:/Users/<USER>/IdeaProjects/study/license-system/license-client/target/apidocs'
-docencoding
'UTF-8'
-Xdoclint:none
-doctitle
'License Client 1.0.0 API'
-linkoffline
'https://docs.oracle.com/en/java/javase/11/docs/api' 'C:/Users/<USER>/IdeaProjects/study/license-system/license-client/target/javadoc-bundle-options'
-use
-version
-windowtitle
'License Client 1.0.0 API'
com.robin.license.client.core
com.robin.license.client.example
com.robin.license.client.hardware
com.robin.license.client.network
com.robin.license.client.sdk
com.robin.license.client.security
C:\Users\<USER>\IdeaProjects\study\license-system\license-common\target\license-common-1.0.0.jar = 1755498180893
C:\Java\Maven\warehouse\commons-codec\commons-codec\1.15\commons-codec-1.15.jar = 1623906977811
C:\Java\Maven\warehouse\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar = 1680690651415
C:\Java\Maven\warehouse\org\bouncycastle\bcpkix-jdk15on\1.70\bcpkix-jdk15on-1.70.jar = 1701306935452
C:\Java\Maven\warehouse\org\bouncycastle\bcutil-jdk15on\1.70\bcutil-jdk15on-1.70.jar = 1701306938848
C:\Java\Maven\warehouse\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar = 1701327807199
C:\Java\Maven\warehouse\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar = 1701327806884
C:\Java\Maven\warehouse\commons-logging\commons-logging\1.2\commons-logging-1.2.jar = 1730943705632
C:\Java\Maven\warehouse\cn\hutool\hutool-all\5.8.22\hutool-all-5.8.22.jar = 1755151739514
C:\Java\Maven\warehouse\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar = 1725333275426
C:\Java\Maven\warehouse\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar = 1730943636932
C:\Java\Maven\warehouse\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar = 1703129055752
C:\Java\Maven\warehouse\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar = 1730943637156
C:\Java\Maven\warehouse\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar = 1701327795339
C:\Java\Maven\warehouse\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar = 1701327794829
C:\Java\Maven\warehouse\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar = 1725355683828
C:\Java\Maven\warehouse\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar = 1725355673074
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\src\main\java = 1755223102437
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\generated-sources\annotations = 1755498183569
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\allclasses-index.html = 1755498205093
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\allpackages-index.html = 1755498205098
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\constant-values.html = 1755498205005
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\copy.svg = 1755498205153
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\element-list = 1755498204940
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\help-doc.html = 1755498205144
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\index-all.html = 1755498205125
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\index.html = 1755498205074
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\link.svg = 1755498205154
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\member-search-index.js = 1755498205104
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\module-search-index.js = 1755498205099
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\overview-summary.html = 1755498205133
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\overview-tree.html = 1755498205069
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\package-search-index.js = 1755498205101
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\script.js = 1755498205152
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\search-page.js = 1755498205161
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\search.html = 1755498205129
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\search.js = 1755498205157
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\stylesheet.css = 1755498205148
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\tag-search-index.js = 1755498205105
C:\Users\<USER>\IdeaProjects\study\license-system\license-client\target\apidocs\type-search-index.js = 1755498205102
