<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d381fb88-e87c-4cfe-a462-93bd65583c97" name="Changes" comment="feat(security): 增加许可证文件结束标记验证并改进SDK初始化流程&#10;&#10;- 在 LicenseValidator 中添加了许可证文件结束标记的读取和验证&#10;- 修改了 LicenseSDK 的自动初始化方法，使用加密保护的参数&#10;- 更新了 SimpleSDKExample 中的 SDK 初始化调用&#10;- 在 LicenseService 中将 verifyLicenseByFingerprint 方法改为公共方法&#10;- 添加了 spring-core 依赖">
      <change afterPath="$PROJECT_DIR$/.idea/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/fastRequest/fastRequestCollection.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/fastRequest/fastRequestCurrentParamGroup.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/fastRequest/fastRequestCurrentProjectLocalConfig.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/license-client/SDK-README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/license-client/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/license-client/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="C:\Users\<USER>\scoop\apps\maven\current" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="C:\Users\<USER>\scoop\apps\maven\current\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="31GX4J1KdybsA31fMxHrrGRghIQ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Application.CipherUtil.executor": "Run",
    "Application.ConfigGenerator.executor": "Debug",
    "Application.SimpleSDKExample.executor": "Debug",
    "Maven.license-common [install].executor": "Run",
    "Maven.license-server [clean].executor": "Run",
    "Maven.license-system [clean,install,-U].executor": "Run",
    "Maven.license-system [clean,install].executor": "Run",
    "Maven.license-system [clean].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "Spring Boot.LicenseServerApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/Users/<USER>/IdeaProjects/study/license-system",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Modules",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "com.augment.tokenmanager.plugin.AugmentTokenManagerConfigurable",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\study\license-system\license-client\src\main\resources" />
      <recent name="C:\Users\<USER>\IdeaProjects\study\license-system\license-server\src\main\java\com\robin\license\server\service" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn clean install" />
      <command value=" mvn clean install -U" />
      <command value="mvn clean install -U" />
      <command value="mvn clean" />
    </option>
  </component>
  <component name="RunManager" selected="Application.SimpleSDKExample">
    <configuration name="CipherUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.robin.license.client.security.CipherUtil" />
      <module name="license-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.robin.license.client.security.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ConfigGenerator" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.robin.license.client.security.ConfigGenerator" />
      <module name="license-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.robin.license.client.security.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SimpleSDKExample" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.robin.license.client.example.SimpleSDKExample" />
      <module name="license-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.robin.license.client.example.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="LicenseServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="license-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.robin.license.server.LicenseServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.SimpleSDKExample" />
        <item itemvalue="Application.CipherUtil" />
        <item itemvalue="Application.ConfigGenerator" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d381fb88-e87c-4cfe-a462-93bd65583c97" name="Changes" comment="" />
      <created>1755151190282</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755151190282</updated>
      <workItem from="1755151191866" duration="28535000" />
      <workItem from="1755484194329" duration="2089000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 初始化项目">
      <option name="closed" value="true" />
      <created>1755222626061</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755222626061</updated>
    </task>
    <task id="LOCAL-00002" summary="build:清理项目构建输出">
      <option name="closed" value="true" />
      <created>1755223183896</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755223183896</updated>
    </task>
    <task id="LOCAL-00003" summary="feat(security): 增加许可证文件结束标记验证并改进SDK初始化流程&#10;&#10;- 在 LicenseValidator 中添加了许可证文件结束标记的读取和验证&#10;- 修改了 LicenseSDK 的自动初始化方法，使用加密保护的参数&#10;- 更新了 SimpleSDKExample 中的 SDK 初始化调用&#10;- 在 LicenseService 中将 verifyLicenseByFingerprint 方法改为公共方法&#10;- 添加了 spring-core 依赖">
      <option name="closed" value="true" />
      <created>1755235248287</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755235248287</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat: 初始化项目" />
    <MESSAGE value="build:清理项目构建输出" />
    <MESSAGE value="feat(security): 增加许可证文件结束标记验证并改进SDK初始化流程&#10;&#10;- 在 LicenseValidator 中添加了许可证文件结束标记的读取和验证&#10;- 修改了 LicenseSDK 的自动初始化方法，使用加密保护的参数&#10;- 更新了 SimpleSDKExample 中的 SDK 初始化调用&#10;- 在 LicenseService 中将 verifyLicenseByFingerprint 方法改为公共方法&#10;- 添加了 spring-core 依赖" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(security): 增加许可证文件结束标记验证并改进SDK初始化流程&#10;&#10;- 在 LicenseValidator 中添加了许可证文件结束标记的读取和验证&#10;- 修改了 LicenseSDK 的自动初始化方法，使用加密保护的参数&#10;- 更新了 SimpleSDKExample 中的 SDK 初始化调用&#10;- 在 LicenseService 中将 verifyLicenseByFingerprint 方法改为公共方法&#10;- 添加了 spring-core 依赖" />
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>