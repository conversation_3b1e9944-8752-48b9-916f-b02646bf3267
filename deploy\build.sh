#!/bin/bash

# License Management System Build Script
# 许可证管理系统构建脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="License Management System"
PROJECT_VERSION="1.0.0"
BUILD_DIR="target"
DIST_DIR="dist"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}  $PROJECT_NAME v$PROJECT_VERSION${NC}"
echo -e "${BLUE}  构建脚本${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查Java环境
check_java() {
    echo -e "${YELLOW}检查Java环境...${NC}"
    if ! command -v java &> /dev/null; then
        echo -e "${RED}错误: 未找到Java环境${NC}"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    echo -e "${GREEN}Java版本: $JAVA_VERSION${NC}"
}

# 检查Maven环境
check_maven() {
    echo -e "${YELLOW}检查Maven环境...${NC}"
    if ! command -v mvn &> /dev/null; then
        echo -e "${RED}错误: 未找到Maven环境${NC}"
        exit 1
    fi
    
    MVN_VERSION=$(mvn -version | head -n 1 | cut -d' ' -f3)
    echo -e "${GREEN}Maven版本: $MVN_VERSION${NC}"
}

# 清理构建目录
clean_build() {
    echo -e "${YELLOW}清理构建目录...${NC}"
    if [ -d "$BUILD_DIR" ]; then
        rm -rf $BUILD_DIR
        echo -e "${GREEN}已清理 $BUILD_DIR 目录${NC}"
    fi
    
    if [ -d "$DIST_DIR" ]; then
        rm -rf $DIST_DIR
        echo -e "${GREEN}已清理 $DIST_DIR 目录${NC}"
    fi
}

# 编译项目
compile_project() {
    echo -e "${YELLOW}编译项目...${NC}"
    mvn clean compile -q
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}项目编译成功${NC}"
    else
        echo -e "${RED}项目编译失败${NC}"
        exit 1
    fi
}

# 运行测试
run_tests() {
    echo -e "${YELLOW}运行测试...${NC}"
    mvn test -q
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}测试通过${NC}"
    else
        echo -e "${RED}测试失败${NC}"
        exit 1
    fi
}

# 打包项目
package_project() {
    echo -e "${YELLOW}打包项目...${NC}"
    mvn package -DskipTests -q
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}项目打包成功${NC}"
    else
        echo -e "${RED}项目打包失败${NC}"
        exit 1
    fi
}

# 创建发布目录
create_dist() {
    echo -e "${YELLOW}创建发布目录...${NC}"
    mkdir -p $DIST_DIR/{server,client,common}
    
    # 复制服务端文件
    if [ -f "license-server/target/license-server-$PROJECT_VERSION.jar" ]; then
        cp license-server/target/license-server-$PROJECT_VERSION.jar $DIST_DIR/server/
        cp deploy/server-start.sh $DIST_DIR/server/
        cp deploy/server-stop.sh $DIST_DIR/server/
        cp license-server/src/main/resources/application.yml $DIST_DIR/server/application-template.yml
        echo -e "${GREEN}服务端文件已复制${NC}"
    fi
    
    # 复制客户端文件
    if [ -f "license-client/target/license-client-$PROJECT_VERSION.jar" ]; then
        cp license-client/target/license-client-$PROJECT_VERSION.jar $DIST_DIR/client/
        cp license-client/src/main/resources/logback-spring.xml $DIST_DIR/client/
        echo -e "${GREEN}客户端文件已复制${NC}"
    fi
    
    # 复制公共文件
    if [ -f "license-common/target/license-common-$PROJECT_VERSION.jar" ]; then
        cp license-common/target/license-common-$PROJECT_VERSION.jar $DIST_DIR/common/
        echo -e "${GREEN}公共组件文件已复制${NC}"
    fi
    
    # 复制文档和脚本
    cp README.md $DIST_DIR/
    cp deploy/*.sh $DIST_DIR/
    cp license-server/src/main/resources/schema.sql $DIST_DIR/
    
    echo -e "${GREEN}发布目录创建完成${NC}"
}

# 创建压缩包
create_archive() {
    echo -e "${YELLOW}创建发布压缩包...${NC}"
    ARCHIVE_NAME="license-system-$PROJECT_VERSION-$(date +%Y%m%d-%H%M%S).tar.gz"
    tar -czf $ARCHIVE_NAME $DIST_DIR/
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}发布包创建成功: $ARCHIVE_NAME${NC}"
        echo -e "${GREEN}文件大小: $(du -h $ARCHIVE_NAME | cut -f1)${NC}"
    else
        echo -e "${RED}发布包创建失败${NC}"
        exit 1
    fi
}

# 显示构建信息
show_build_info() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  构建完成${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${GREEN}项目名称: $PROJECT_NAME${NC}"
    echo -e "${GREEN}项目版本: $PROJECT_VERSION${NC}"
    echo -e "${GREEN}构建时间: $(date)${NC}"
    echo -e "${GREEN}发布目录: $DIST_DIR/${NC}"
    
    if [ -f *.tar.gz ]; then
        echo -e "${GREEN}发布包: $(ls *.tar.gz)${NC}"
    fi
    
    echo -e "${BLUE}========================================${NC}"
}

# 主函数
main() {
    # 解析命令行参数
    SKIP_TESTS=false
    SKIP_PACKAGE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --skip-package)
                SKIP_PACKAGE=true
                shift
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --skip-tests    跳过测试"
                echo "  --skip-package  跳过打包"
                echo "  --help, -h      显示帮助信息"
                exit 0
                ;;
            *)
                echo -e "${RED}未知选项: $1${NC}"
                exit 1
                ;;
        esac
    done
    
    # 执行构建步骤
    check_java
    check_maven
    clean_build
    compile_project
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    else
        echo -e "${YELLOW}跳过测试${NC}"
    fi
    
    if [ "$SKIP_PACKAGE" = false ]; then
        package_project
        create_dist
        create_archive
    else
        echo -e "${YELLOW}跳过打包${NC}"
    fi
    
    show_build_info
}

# 执行主函数
main "$@"
