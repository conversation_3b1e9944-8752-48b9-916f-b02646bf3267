package com.robin.license.client.network;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import com.google.gson.reflect.TypeToken;
import com.robin.license.client.hardware.MachineInfoCollector;
import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.crypto.CryptoUtil;
import com.robin.license.common.dto.ApiResponse;
import com.robin.license.common.dto.LicenseFileResponse;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.dto.SimpleMachineInfo;
import com.robin.license.common.exception.LicenseException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 许可证网络客户端
 * 负责与许可证服务器的网络通信
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class LicenseClient {
    
    private final String serverUrl;
    private final CloseableHttpClient httpClient;
    private final Gson gson;
    private final RequestConfig requestConfig;
    
    /**
     * 构造函数
     * 
     * @param serverUrl 服务器URL
     */
    public LicenseClient(String serverUrl) {
        this.serverUrl = serverUrl.endsWith("/") ? serverUrl.substring(0, serverUrl.length() - 1) : serverUrl;

        // 配置Gson，添加LocalDateTime序列化器
        this.gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new LocalDateTimeSerializer())
                .create();
        
        // 配置HTTP客户端
        this.requestConfig = RequestConfig.custom()
                .setConnectTimeout(LicenseConstants.Http.DEFAULT_TIMEOUT)
                .setSocketTimeout(LicenseConstants.Http.DEFAULT_TIMEOUT)
                .setConnectionRequestTimeout(LicenseConstants.Http.DEFAULT_TIMEOUT)
                .build();
        
        this.httpClient = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig)
                .build();
        
        log.info("许可证客户端初始化完成，服务器地址: {}", this.serverUrl);
    }
    
    /**
     * 验证许可证
     * 
     * @param licenseInfo 许可证信息
     * @return 验证结果
     * @throws LicenseException 验证失败时抛出异常
     */
    public boolean verifyLicense(LicenseInfo licenseInfo) throws LicenseException {
        try {
            // 收集机器信息
            MachineInfo machineInfo = MachineInfoCollector.collectMachineInfo();
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("licenseId", licenseInfo.getLicenseId());
            requestData.put("machineId", licenseInfo.getMachineId());
            requestData.put("machineInfo", machineInfo);
            requestData.put("timestamp", System.currentTimeMillis());
            
            // 发送验证请求
            ApiResponse<Boolean> response = sendPostRequest(
                    LicenseConstants.Http.VERIFY_PATH, 
                    requestData, 
                    Boolean.class
            );
            
            if (response.isSuccess()) {
                return response.getData() != null && response.getData();
            } else {
                throw new LicenseException.ServerException("服务器验证失败: " + response.getMessage());
            }
            
        } catch (LicenseException e) {
            throw e;
        } catch (Exception e) {
            log.error("许可证验证网络请求失败", e);
            throw new LicenseException.NetworkException("网络验证失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步许可证（统一的注册和验证接口）
     *
     * @param machineInfo 机器信息
     * @return 是否同步成功
     */
    public boolean syncLicense(MachineInfo machineInfo) {
        try {
            log.info("向服务端同步许可证，机器ID: {}", machineInfo.getMachineId());

            // 转换为简化的机器信息以减少网络传输数据
            SimpleMachineInfo simpleMachineInfo = SimpleMachineInfo.fromMachineInfo(machineInfo);

            // 发送同步请求
            ApiResponse<LicenseFileResponse> response = sendPostRequest(
                    LicenseConstants.Http.SYNC_PATH,
                    simpleMachineInfo,
                    LicenseFileResponse.class);

            if (response.isSuccess() && response.getData() != null) {
                LicenseFileResponse fileResponse = response.getData();

                // 保存许可证文件和公钥文件到本地
                boolean saved = saveLicenseFiles(fileResponse);

                if (saved) {
                    log.info("许可证同步成功，文件已更新");
                    return true;
                } else {
                    log.warn("许可证同步成功，但文件保存失败");
                    return false;
                }
            } else {
                log.warn("许可证同步请求失败: {}", response.getMessage());
                return false;
            }

        } catch (Exception e) {
            log.error("许可证同步网络失败", e);
            return false;
        }
    }

    /**
     * 保存许可证文件和公钥文件到本地
     *
     * @param fileResponse 文件响应
     * @return 是否保存成功
     */
    private boolean saveLicenseFiles(LicenseFileResponse fileResponse) {
        try {
            // 确保目录存在
            String userHome = System.getProperty("user.home");
            File licenseDir = new File(userHome, LicenseConstants.File.LICENSE_DIR);
            licenseDir.mkdirs();

            // 保存许可证文件
            File licenseFile = new File(licenseDir, LicenseConstants.File.LICENSE_FILE);
            byte[] licenseContent = CryptoUtil.base64Decode(fileResponse.getLicenseFileContent());
            try (FileOutputStream fos = new FileOutputStream(licenseFile)) {
                fos.write(licenseContent);
            }

            // 保存公钥文件
            File publicKeyFile = new File(licenseDir, LicenseConstants.File.PUBLIC_KEY_FILE);
            try (FileOutputStream fos = new FileOutputStream(publicKeyFile)) {
                fos.write(fileResponse.getPublicKeyContent().getBytes(StandardCharsets.UTF_8));
            }

            log.info("许可证文件和公钥文件保存成功");
            log.info("许可证文件: {}", licenseFile.getAbsolutePath());
            log.info("公钥文件: {}", publicKeyFile.getAbsolutePath());

            return true;

        } catch (Exception e) {
            log.error("保存许可证文件失败", e);
            return false;
        }
    }

    /**
     * 发送POST请求
     * 
     * @param path 请求路径
     * @param requestData 请求数据
     * @param responseType 响应数据类型
     * @param <T> 响应数据类型
     * @return API响应
     * @throws IOException 网络异常
     */
    @SuppressWarnings("unchecked")
    private <T> ApiResponse<T> sendPostRequest(String path, Object requestData, Class<T> responseType) 
            throws IOException {
        
        String url = serverUrl + path;
        HttpPost httpPost = new HttpPost(url);
        
        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json; charset=UTF-8");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("User-Agent", "LicenseClient/1.0.0");
        
        // 设置请求体
        String jsonData = gson.toJson(requestData);
        StringEntity entity = new StringEntity(jsonData, StandardCharsets.UTF_8);
        httpPost.setEntity(entity);
        
        log.debug("发送请求到: {}, 数据: {}", url, jsonData);
        
        // 执行请求
        HttpResponse response = httpClient.execute(httpPost);
        HttpEntity responseEntity = response.getEntity();
        
        if (responseEntity != null) {
            String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
            log.debug("收到响应: {}", responseBody);
            
            // 解析响应，使用TypeToken来保持泛型信息
            Type responseTypeToken;
            if (responseType == Boolean.class) {
                responseTypeToken = new TypeToken<ApiResponse<Boolean>>(){}.getType();
            } else if (responseType == LicenseInfo.class) {
                responseTypeToken = new TypeToken<ApiResponse<LicenseInfo>>(){}.getType();
            } else if (responseType == LicenseFileResponse.class) {
                responseTypeToken = new TypeToken<ApiResponse<LicenseFileResponse>>(){}.getType();
            } else {
                responseTypeToken = new TypeToken<ApiResponse<Object>>(){}.getType();
            }

            return gson.fromJson(responseBody, responseTypeToken);
        }
        
        return ApiResponse.error(LicenseConstants.ErrorCode.NETWORK_ERROR, "响应为空");
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
        } catch (IOException e) {
            log.error("关闭HTTP客户端失败", e);
        }
    }

    /**
     * LocalDateTime的JSON序列化器
     * 将LocalDateTime序列化为ISO-8601格式的字符串
     */
    private static class LocalDateTimeSerializer implements JsonSerializer<LocalDateTime> {
        private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        @Override
        public JsonElement serialize(LocalDateTime src, Type typeOfSrc, JsonSerializationContext context) {
            return new JsonPrimitive(src.format(FORMATTER));
        }
    }
}
