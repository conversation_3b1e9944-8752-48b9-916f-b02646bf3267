package com.robin.license.client.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Scanner;

/**
 * 加密工具类
 * 提供字节加密解密、配置文件生成和参数获取功能
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class CipherUtil {

    /**
     * 默认加密密钥
     */
    public static final String DEFAULTKEY = "LicenseSDKProtect";

    /**
     * 数据填充字节，用于分隔数据项
     */
    public static final byte[] m_datapadding = {0x7F};

    /**
     * 文件结束标记
     */
    public static final byte[] m_ending = {0x00, 0x00};

    /**
     * EXE文件头标识，用于伪装成exe文件
     */
    public static final byte[] mzHeader = {0x4D, 0x5A, 0x50, 0x00, 0x02, 0x00, 0x00, 0x00, 0x04, 0x00, 0x0f, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

    /**
     * 加密算法
     */
    private static final String ALGORITHM = "AES";

    /**
     * 加密字节数组
     *
     * @param data 要加密的数据
     * @param key 加密密钥
     * @return 加密后的字节数组
     */
    public static byte[] encryptByte(byte[] data, byte[] key) {
        try {
            // 生成AES密钥
            SecretKeySpec secretKey = generateSecretKey(key);

            // 创建加密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            // 执行加密
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 解密字节数组
     *
     * @param encryptedData 要解密的数据
     * @param key 解密密钥
     * @return 解密后的字节数组
     */
    public static byte[] decryptByte(byte[] encryptedData, byte[] key) {
        try {
            // 生成AES密钥
            SecretKeySpec secretKey = generateSecretKey(key);

            // 创建解密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            // 执行解密
            return cipher.doFinal(encryptedData);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }

    /**
     * 生成AES密钥
     * 使用SHA-256对原始密钥进行哈希，然后取前16字节作为AES密钥
     *
     * @param key 原始密钥
     * @return AES密钥规范
     */
    private static SecretKeySpec generateSecretKey(byte[] key) {
        try {
            // 使用SHA-256对密钥进行哈希
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashedKey = digest.digest(key);

            // 取前16字节作为AES密钥
            byte[] aesKey = new byte[16];
            System.arraycopy(hashedKey, 0, aesKey, 0, 16);

            return new SecretKeySpec(aesKey, ALGORITHM);
        } catch (Exception e) {
            throw new RuntimeException("生成密钥失败", e);
        }
    }

    /**
     * 简单的XOR加密（作为备用方案）
     *
     * @param data 要加密的数据
     * @param key 加密密钥
     * @return 加密后的字节数组
     */
    public static byte[] xorEncrypt(byte[] data, byte[] key) {
        byte[] result = new byte[data.length];
        for (int i = 0; i < data.length; i++) {
            result[i] = (byte) (data[i] ^ key[i % key.length]);
        }
        return result;
    }

    /**
     * 简单的XOR解密（作为备用方案）
     *
     * @param encryptedData 要解密的数据
     * @param key 解密密钥
     * @return 解密后的字节数组
     */
    public static byte[] xorDecrypt(byte[] encryptedData, byte[] key) {
        // XOR加密和解密是相同的操作
        return xorEncrypt(encryptedData, key);
    }

    // ========== 参数保护功能 ==========

    /**
     * 关键字映射表
     */
    private static final Map<Integer, String> keywordMap = new HashMap<>();

    /**
     * 单例实例
     */
    private static volatile CipherUtil instance;

    static {
        // 在类加载时加载配置文件
        loadKeywordFile();
    }

    /**
     * 获取单例实例
     */
    public static CipherUtil getInstance() {
        if (instance == null) {
            synchronized (CipherUtil.class) {
                if (instance == null) {
                    instance = new CipherUtil();
                }
            }
        }
        return instance;
    }

    /**
     * 根据数字ID获取关键字值
     *
     * @param key 数字ID
     * @return 对应的值
     */
    public String retKeyword(Integer key) {
        if (keywordMap.containsKey(key)) {
            return keywordMap.get(key);
        }
        return null;
    }

    /**
     * 从keyword.dat文件加载配置
     */
    private static void loadKeywordFile() {
        try {
            InputStream keywordIn = null;
            File keywordFile = new File("keyword.dat");

            if (keywordFile.exists()) {
                keywordIn = new FileInputStream(keywordFile);
            } else {
                keywordIn = CipherUtil.class.getClassLoader().getResourceAsStream("keyword.dat");
            }

            if (!ObjectUtils.isEmpty(keywordIn)) {
                try (DataInputStream dataIn = new DataInputStream(keywordIn)) {
                    dataIn.read(mzHeader); // 读取文件头
                    byte[] readbyte = new byte[1];

                    while (dataIn.available() > 0) {
                        dataIn.read(readbyte);
                        if (readbyte[0] != m_ending[0]) {
                            Integer key = dataIn.readInt();
                            Integer length = dataIn.readInt();
                            byte[] encryptBytes = new byte[length];
                            dataIn.read(encryptBytes);
                            byte[] decryptByte = decryptByte(encryptBytes, DEFAULTKEY.getBytes());
                            String val = new String(decryptByte, StandardCharsets.UTF_8);
                            keywordMap.put(key, val);
                        }
                    }
                }
            } else {
                // 使用默认参数
                initializeDefaultParams();
            }
        } catch (Exception ex) {
            log.error("加载配置文件失败", ex);
            // 使用默认参数
            initializeDefaultParams();
        }
    }

    /**
     * 初始化默认参数
     */
    private static void initializeDefaultParams() {
        keywordMap.put(115, "http://localhost:8080");
        keywordMap.put(116, "9091");
        keywordMap.put(117, "30000");
        keywordMap.put(118, "3");
    }

    /**
     * 从配置映射生成keyword.dat文件
     *
     * @param outputFile 输出文件路径
     * @param configMap 配置映射
     */
    public static void generateKeywordFile(String outputFile, Map<Integer, String> configMap) {
        try (DataOutputStream outputStream = new DataOutputStream(new FileOutputStream(outputFile))) {
            outputStream.write(mzHeader);

            for (Map.Entry<Integer, String> entry : configMap.entrySet()) {
                outputStream.write(m_datapadding);
                outputStream.writeInt(entry.getKey());
                byte[] cryptBytes = encryptByte(entry.getValue().getBytes("UTF-8"), DEFAULTKEY.getBytes());
                outputStream.writeInt(cryptBytes.length);
                outputStream.write(cryptBytes);
            }
            outputStream.write(m_ending);

            System.out.println("加密配置文件已生成: " + outputFile);
        } catch (IOException ex) {
            log.error("生成配置文件失败", ex);
        }
    }

    /**
     * 从properties文件生成keyword.dat
     *
     * @param configFilePath 配置文件路径
     * @param outputPath 输出文件路径
     */
    public static void generateFromProperties(String configFilePath, String outputPath) {
        try {
            Properties props = new Properties();
            InputStream inputStream = null;

            // 首先尝试从文件系统读取
            File configFile = new File(configFilePath);
            if (configFile.exists()) {
                inputStream = new FileInputStream(configFile);
            } else {
                // 尝试从类路径读取
                inputStream = CipherUtil.class.getClassLoader().getResourceAsStream(configFilePath);
                if (inputStream == null) {
                    // 尝试去掉路径前缀，只用文件名
                    String fileName = configFilePath.substring(configFilePath.lastIndexOf('/') + 1);
                    inputStream = CipherUtil.class.getClassLoader().getResourceAsStream(fileName);
                }
            }

            if (inputStream == null) {
                System.err.println("找不到配置文件: " + configFilePath);
                return;
            }

            try (InputStream is = inputStream) {
                props.load(is);
            }

            Map<Integer, String> configMap = new HashMap<>();
            for (String key : props.stringPropertyNames()) {
                try {
                    Integer keyInt = Integer.parseInt(key);
                    configMap.put(keyInt, props.getProperty(key));
                } catch (NumberFormatException e) {
                    System.out.println("跳过非数字键: " + key);
                }
            }

            generateKeywordFile(outputPath, configMap);
        } catch (Exception e) {
            log.error("从properties生成失败", e);
        }
    }



    /**
     * 交互式生成配置文件
     */
    public static void interactiveGenerate() {
        Scanner scanner = new Scanner(System.in);
        Map<Integer, String> configMap = new HashMap<>();

        System.out.println("=== 关键字配置生成器 ===");
        System.out.println("输入配置项（格式：数字=值），输入 'done' 完成");

        while (true) {
            System.out.print("请输入配置项 (数字=值) 或 'done': ");
            String input = scanner.nextLine().trim();

            if ("done".equalsIgnoreCase(input)) {
                break;
            }

            if (input.contains("=")) {
                String[] parts = input.split("=", 2);
                if (parts.length == 2) {
                    try {
                        int id = Integer.parseInt(parts[0].trim());
                        String value = parts[1].trim();
                        configMap.put(id, value);
                        System.out.println("已添加: " + id + " -> " + value);
                    } catch (NumberFormatException e) {
                        System.out.println("错误：ID必须是数字");
                    }
                } else {
                    System.out.println("错误：格式不正确，请使用 数字=值");
                }
            } else {
                System.out.println("错误：格式不正确，请使用 数字=值");
            }
        }

        if (configMap.isEmpty()) {
            System.out.println("没有配置项，退出");
            return;
        }

        System.out.print("请输入输出文件名 (默认: license-client/src/main/resources/keyword.dat): ");
        String outputFile = scanner.nextLine().trim();
        if (outputFile.isEmpty()) {
            outputFile = "license-client/src/main/resources/keyword.dat";
        }

        generateKeywordFile(outputFile, configMap);

        System.out.println("\n使用方法:");
        System.out.println("String value = CipherUtil.getInstance().retKeyword(数字ID);");
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        if (args.length == 2) {
            // 从配置文件生成
            generateFromProperties(args[0], args[1]);
        } else if (args.length == 1) {
            // 使用默认输出文件名
            generateFromProperties(args[0], "license-client/src/main/resources/keyword.dat");
        } else if (args.length == 0) {
            // 尝试使用默认配置文件
            System.out.println("尝试使用默认配置文件 config.properties...");
            generateFromProperties("config.properties", "license-client/src/main/resources/keyword.dat");

            // 测试读取参数
            System.out.println("\n测试读取参数:");
            String serverUrl = CipherUtil.getInstance().retKeyword(115);
            System.out.println("服务器URL: " + serverUrl);
            String port = CipherUtil.getInstance().retKeyword(116);
            System.out.println("端口: " + port);
            String timeout = CipherUtil.getInstance().retKeyword(117);
            System.out.println("超时: " + timeout);
            String retryCount = CipherUtil.getInstance().retKeyword(118);
            System.out.println("重试次数: " + retryCount);
        } else {
            System.out.println("用法:");
            System.out.println("1. 无参数模式: java CipherUtil (使用默认config.properties)");
            System.out.println("2. 单参数模式: java CipherUtil <配置文件> (输出到keyword.dat)");
            System.out.println("3. 双参数模式: java CipherUtil <配置文件> <输出文件>");
            System.out.println("\n配置文件格式示例:");
            System.out.println("115=http://localhost:8080");
            System.out.println("116=9090");
            System.out.println("117=30000");
            System.out.println("118=3");
        }
    }

}
