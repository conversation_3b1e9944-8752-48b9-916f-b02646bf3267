package com.robin.license.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 许可证信息数据传输对象
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LicenseInfo {
    
    /**
     * 许可证ID
     */
    private String licenseId;
    
    /**
     * 客户ID
     */
    private String customerId;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 产品版本
     */
    private String productVersion;
    
    /**
     * 许可证类型
     */
    private String licenseType;
    
    /**
     * 机器标识
     */
    private String machineId;
    
    /**
     * 许可证状态
     */
    private String status;
    
    /**
     * 签发时间
     */
    private LocalDateTime issueTime;
    
    /**
     * 生效时间
     */
    private LocalDateTime effectiveTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 最大用户数
     */
    private Integer maxUsers;
    
    /**
     * 最大并发数
     */
    private Integer maxConcurrent;
    
    /**
     * 功能模块列表
     */
    private String[] modules;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;
    
    /**
     * 数字签名
     */
    private String signature;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 备注信息
     */
    private String remarks;
    
    /**
     * 检查许可证是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        if (status == null) {
            return false;
        }
        return "1".equals(status) || "VALID".equals(status);
    }
    
    /**
     * 检查许可证是否过期
     * 
     * @return 是否过期
     */
    public boolean isExpired() {
        if (expireTime == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 检查许可证是否在有效期内
     * 
     * @return 是否在有效期内
     */
    public boolean isInValidPeriod() {
        LocalDateTime now = LocalDateTime.now();
        
        if (effectiveTime != null && now.isBefore(effectiveTime)) {
            return false;
        }
        
        if (expireTime != null && now.isAfter(expireTime)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取剩余天数
     * 
     * @return 剩余天数，-1表示已过期
     */
    public long getRemainingDays() {
        if (expireTime == null) {
            return Long.MAX_VALUE;
        }
        
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(expireTime)) {
            return -1;
        }
        
        return java.time.Duration.between(now, expireTime).toDays();
    }
}
