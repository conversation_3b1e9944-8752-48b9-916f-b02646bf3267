package com.robin.license.server.repository;

import com.robin.license.server.entity.License;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 许可证数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface LicenseRepository extends JpaRepository<License, Long> {
    
    /**
     * 根据许可证ID查找
     * 
     * @param licenseId 许可证ID
     * @return 许可证信息
     */
    Optional<License> findByLicenseId(String licenseId);
    

    
    /**
     * 根据机器ID查找许可证
     * 
     * @param machineId 机器ID
     * @return 许可证列表
     */
    List<License> findByMachineId(String machineId);
    
    /**
     * 根据状态查找许可证
     * 
     * @param status 状态
     * @return 许可证列表
     */
    List<License> findByStatus(String status);
    
    /**
     * 查找即将过期的许可证
     * 
     * @param expireTime 过期时间阈值
     * @return 许可证列表
     */
    @Query("SELECT l FROM License l WHERE l.expireTime <= :expireTime AND l.status = 'VALID'")
    List<License> findExpiringLicenses(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 查找已过期的许可证
     * 
     * @param currentTime 当前时间
     * @return 许可证列表
     */
    @Query("SELECT l FROM License l WHERE l.expireTime < :currentTime AND l.status = 'VALID'")
    List<License> findExpiredLicenses(@Param("currentTime") LocalDateTime currentTime);
    

    
    /**
     * 统计有效许可证数量
     * 
     * @return 有效许可证数量
     */
    @Query("SELECT COUNT(l) FROM License l WHERE l.status = 'VALID' AND l.expireTime > CURRENT_TIMESTAMP")
    long countValidLicenses();
    
    /**
     * 统计过期许可证数量
     * 
     * @return 过期许可证数量
     */
    @Query("SELECT COUNT(l) FROM License l WHERE l.expireTime < CURRENT_TIMESTAMP")
    long countExpiredLicenses();
    
    /**
     * 根据许可证类型统计数量
     * 
     * @param licenseType 许可证类型
     * @return 许可证数量
     */
    long countByLicenseType(String licenseType);
    
    /**
     * 查找最近验证的许可证
     * 
     * @param limit 限制数量
     * @return 许可证列表
     */
    @Query("SELECT l FROM License l WHERE l.lastVerifyTime IS NOT NULL ORDER BY l.lastVerifyTime DESC")
    List<License> findRecentlyVerifiedLicenses(@Param("limit") int limit);
    
    /**
     * 根据创建时间范围查找许可证
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 许可证列表
     */
    @Query("SELECT l FROM License l WHERE l.createTime >= :startTime AND l.createTime <= :endTime")
    List<License> findByCreateTimeBetween(@Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);
    

    
    /**
     * 检查许可证ID是否存在
     * 
     * @param licenseId 许可证ID
     * @return 是否存在
     */
    boolean existsByLicenseId(String licenseId);
}
