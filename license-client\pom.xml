<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.robin</groupId>
        <artifactId>license-system</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>license-client</artifactId>
    <packaging>jar</packaging>

    <name>License Client</name>
    <description>授权系统客户端</description>

    <dependencies>
        <!-- 公共模块 -->
        <dependency>
            <groupId>com.robin</groupId>
            <artifactId>license-common</artifactId>
            <version>1.0.0</version>
        </dependency>
        
        <!-- HTTP客户端 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>

        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.2</version>
        </dependency>
        
        <!-- 工具类库 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        
        <!-- 日志和工具 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
    </dependencies>
    
    <build>
        <finalName>license-client-sdk-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>

            <!-- Maven Shade Plugin - 创建包含所有依赖的fat jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <!-- 创建可执行jar -->
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>sdk</shadedClassifierName>

                            <!-- 排除不需要的文件 -->
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>META-INF/MANIFEST.MF</exclude>
                                        <exclude>META-INF/LICENSE*</exclude>
                                        <exclude>META-INF/NOTICE*</exclude>
                                        <exclude>META-INF/DEPENDENCIES*</exclude>
                                    </excludes>
                                </filter>
                                <!-- 保留lombok注解处理器相关文件 -->
                                <filter>
                                    <artifact>org.projectlombok:lombok</artifact>
                                    <excludes>
                                        <exclude>**</exclude>
                                    </excludes>
                                </filter>
                            </filters>

                            <!-- 重定位包名避免冲突 -->
                            <relocations>
                                <!-- 重定位第三方库到SDK专用包名下 -->
                                <relocation>
                                    <pattern>org.apache.http</pattern>
                                    <shadedPattern>com.robin.license.sdk.shaded.apache.http</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.apache.commons</pattern>
                                    <shadedPattern>com.robin.license.sdk.shaded.apache.commons</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.google.gson</pattern>
                                    <shadedPattern>com.robin.license.sdk.shaded.gson</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>cn.hutool</pattern>
                                    <shadedPattern>com.robin.license.sdk.shaded.hutool</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.slf4j</pattern>
                                    <shadedPattern>com.robin.license.sdk.shaded.slf4j</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>ch.qos.logback</pattern>
                                    <shadedPattern>com.robin.license.sdk.shaded.logback</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.springframework</pattern>
                                    <shadedPattern>com.robin.license.sdk.shaded.springframework</shadedPattern>
                                </relocation>
                            </relocations>

                            <!-- 合并服务文件 -->
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Built-By>Robin License System</Built-By>
                                        <Build-Jdk>${java.version}</Build-Jdk>
                                        <Implementation-Title>${project.name}</Implementation-Title>
                                        <Implementation-Version>${project.version}</Implementation-Version>
                                        <Implementation-Vendor>Robin</Implementation-Vendor>
                                        <Specification-Title>License Client SDK</Specification-Title>
                                        <Specification-Version>${project.version}</Specification-Version>
                                        <Specification-Vendor>Robin</Specification-Vendor>
                                    </manifestEntries>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Source Plugin - 生成源码jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Maven Javadoc Plugin - 生成API文档 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.5.0</version>
                <configuration>
                    <source>11</source>
                    <encoding>UTF-8</encoding>
                    <charset>UTF-8</charset>
                    <docencoding>UTF-8</docencoding>
                    <doclint>none</doclint>
                    <quiet>true</quiet>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- ProGuard Plugin - 代码混淆和保护 -->
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.6.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <proguardVersion>7.3.2</proguardVersion>
                    <injar>${project.build.finalName}-sdk.jar</injar>
                    <outjar>${project.build.finalName}-sdk-obfuscated.jar</outjar>
                    <includeDependency>false</includeDependency>
                    <libs>
                        <lib>${java.home}/lib/rt.jar</lib>
                        <lib>${java.home}/lib/jce.jar</lib>
                    </libs>
                    <options>
                        <!-- 保持主要API接口不被混淆 -->
                        <option>-keep public class com.robin.license.client.sdk.LicenseSDK {
                            public static com.robin.license.client.sdk.LicenseSDK getInstance();
                            public void autoInitialize();
                            public void autoInitialize(boolean);
                            public boolean isLicenseValid();
                            public com.robin.license.common.dto.LicenseInfo getLicenseInfo();
                            public void shutdown();
                        }</option>

                        <!-- 保持DTO类不被混淆 -->
                        <option>-keep public class com.robin.license.common.dto.** {
                            public *;
                        }</option>

                        <!-- 保持异常类 -->
                        <option>-keep public class com.robin.license.common.exception.** {
                            public *;
                        }</option>

                        <!-- 混淆所有其他类 -->
                        <option>-obfuscationdictionary proguard-dict.txt</option>
                        <option>-classobfuscationdictionary proguard-dict.txt</option>

                        <!-- 字符串加密 -->
                        <option>-adaptclassstrings</option>

                        <!-- 移除调试信息 -->
                        <option>-assumenosideeffects class android.util.Log {
                            public static *** d(...);
                            public static *** v(...);
                            public static *** i(...);
                        }</option>

                        <!-- 控制流混淆 -->
                        <option>-repackageclasses 'obf'</option>
                        <option>-allowaccessmodification</option>

                        <!-- 防止反编译优化 -->
                        <option>-optimizations !code/simplification/arithmetic,!field/*,!class/merging/*</option>
                        <option>-optimizationpasses 3</option>

                        <!-- 保持注解 -->
                        <option>-keepattributes *Annotation*</option>

                        <!-- 不警告缺失的类 -->
                        <option>-dontwarn **</option>
                        <option>-ignorewarnings</option>
                    </options>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.guardsquare</groupId>
                        <artifactId>proguard-base</artifactId>
                        <version>7.3.2</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>
