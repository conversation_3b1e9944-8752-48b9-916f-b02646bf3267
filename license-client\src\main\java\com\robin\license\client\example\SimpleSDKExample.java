package com.robin.license.client.example;

import com.robin.license.client.sdk.LicenseSDK;
import com.robin.license.common.dto.LicenseInfo;

/**
 * 简单SDK使用示例
 * 展示最简单的集成方式
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
public class SimpleSDKExample {
    
    public static void main(String[] args) {
        System.out.println("=== 应用程序启动 ===");

        // 自动化许可证SDK - 一行代码搞定
        LicenseSDK sdk = LicenseSDK.getInstance();
        sdk.autoInitialize();

        System.out.println("许可证SDK初始化完成");

        // 模拟应用运行
        runApplication();
    }
    
    /**
     * 模拟应用运行
     */
    private static void runApplication() {
        System.out.println("\n=== 应用程序运行中 ===");

        LicenseSDK sdk = LicenseSDK.getInstance();

        // 检查许可证状态
        if (sdk.isLicenseValid()) {
            LicenseInfo licenseInfo = sdk.getLicenseInfo();
            System.out.println("许可证有效 - 客户: " + licenseInfo.getCustomerName());
            System.out.println("业务逻辑正常执行...");
        } else {
            System.err.println("许可证无效");
        }

        // 关闭SDK
//        sdk.shutdown();
    }
}
