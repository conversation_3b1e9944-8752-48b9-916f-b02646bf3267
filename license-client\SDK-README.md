# License Client SDK 使用指南

## 概述

License Client SDK 是一个独立的许可证验证客户端库，提供自动化的许可证管理功能。SDK 采用 fat jar 打包方式，包含所有必要的依赖，第三方公司可以直接集成到自己的 Java 应用中。

## 特性

- **自动化集成**: 一行代码完成许可证验证
- **机器绑定**: 基于硬件指纹的机器绑定验证
- **定时验证**: 自动定时验证许可证有效性
- **加密保护**: 配置参数加密存储，防止篡改
- **跨平台支持**: 支持 Windows、Linux、macOS
- **零依赖冲突**: 使用 shade 技术避免依赖冲突

## 快速开始

### 1. 添加 SDK 到项目

将 `license-client-sdk-1.0.0-sdk.jar` 添加到项目的 classpath 中。

**Maven 项目**:
```xml
<dependency>
    <groupId>com.robin</groupId>
    <artifactId>license-client</artifactId>
    <version>1.0.0</version>
    <classifier>sdk</classifier>
    <scope>system</scope>
    <systemPath>${project.basedir}/lib/license-client-sdk-1.0.0-sdk.jar</systemPath>
</dependency>
```

**Gradle 项目**:
```gradle
dependencies {
    implementation files('lib/license-client-sdk-1.0.0-sdk.jar')
}
```

### 2. 配置文件

在应用的 classpath 根目录下放置以下文件：

- `keyword.dat`: 加密配置文件（由许可证提供方提供）
- `public.key`: 公钥文件（由许可证提供方提供）

### 3. 基本使用

```java
import com.robin.license.client.sdk.LicenseSDK;
import com.robin.license.common.dto.LicenseInfo;

public class YourApplication {
    
    public static void main(String[] args) {
        // 1. 自动初始化许可证SDK
        LicenseSDK sdk = LicenseSDK.getInstance();
        sdk.autoInitialize();
        
        // 2. 检查许可证状态
        if (sdk.isLicenseValid()) {
            LicenseInfo licenseInfo = sdk.getLicenseInfo();
            System.out.println("许可证有效 - 客户: " + licenseInfo.getCustomerName());
            
            // 3. 执行业务逻辑
            runYourBusinessLogic();
        } else {
            System.err.println("许可证无效，应用无法运行");
            System.exit(1);
        }
    }
    
    private static void runYourBusinessLogic() {
        // 您的业务逻辑代码
        System.out.println("业务逻辑正常执行...");
    }
}
```

### 4. Spring Boot 集成

```java
@SpringBootApplication
public class YourSpringBootApplication {
    
    public static void main(String[] args) {
        // 在 Spring Boot 启动前初始化许可证
        LicenseSDK sdk = LicenseSDK.getInstance();
        sdk.autoInitialize();
        
        if (sdk.isLicenseValid()) {
            SpringApplication.run(YourSpringBootApplication.class, args);
        } else {
            System.err.println("许可证验证失败，应用无法启动");
            System.exit(1);
        }
    }
}
```

或者使用 Bean 方式：

```java
@Configuration
public class LicenseConfig {
    
    @Bean
    @DependsOn("licenseSDK")
    public String licenseValidator() {
        LicenseSDK sdk = LicenseSDK.getInstance();
        if (!sdk.isLicenseValid()) {
            throw new RuntimeException("许可证验证失败");
        }
        return "license-valid";
    }
    
    @Bean("licenseSDK")
    public LicenseSDK licenseSDK() {
        LicenseSDK sdk = LicenseSDK.getInstance();
        sdk.autoInitialize();
        return sdk;
    }
}
```

## API 参考

### LicenseSDK 主要方法

```java
// 获取SDK实例（单例模式）
LicenseSDK sdk = LicenseSDK.getInstance();

// 自动初始化（严格模式，无证书直接退出）
sdk.autoInitialize();

// 自动初始化（指定模式）
sdk.autoInitialize(boolean strictMode);

// 检查许可证是否有效
boolean isValid = sdk.isLicenseValid();

// 获取许可证信息
LicenseInfo licenseInfo = sdk.getLicenseInfo();

// 关闭SDK（释放资源）
sdk.shutdown();
```

### LicenseInfo 许可证信息

```java
public class LicenseInfo {
    private String licenseId;        // 许可证ID
    private String customerName;     // 客户名称
    private String machineId;        // 机器ID
    private LocalDateTime issueTime; // 签发时间
    private LocalDateTime expireTime;// 过期时间
    private Map<String, Object> features; // 功能特性
    
    // getter 方法...
}
```

## 配置说明

### 严格模式 vs 宽松模式

- **严格模式** (`strictMode = true`): 许可证验证失败时，应用直接退出
- **宽松模式** (`strictMode = false`): 许可证验证失败时，应用继续运行但功能受限

### 定时验证

SDK 会自动启动定时验证任务，默认每小时验证一次许可证有效性。

## 故障排除

### 常见问题

1. **许可证文件不存在**
   - 确保 `license.dat` 文件在正确位置
   - 检查文件权限

2. **机器绑定失败**
   - 许可证可能绑定到其他机器
   - 联系许可证提供方重新生成

3. **网络连接失败**
   - 检查许可证服务器地址配置
   - 确保网络连接正常

4. **依赖冲突**
   - SDK 使用 shade 技术避免冲突
   - 如仍有问题，检查类路径配置

### 日志配置

SDK 使用 SLF4J 进行日志记录，可以通过配置 logback 来控制日志输出：

```xml
<!-- logback-spring.xml -->
<configuration>
    <logger name="com.robin.license" level="INFO"/>
    <!-- 其他配置... -->
</configuration>
```

## 技术支持

如有技术问题，请联系许可证提供方获取支持。

## 版本信息

- SDK 版本: 1.0.0
- Java 版本要求: JDK 8+
- 支持平台: Windows, Linux, macOS
