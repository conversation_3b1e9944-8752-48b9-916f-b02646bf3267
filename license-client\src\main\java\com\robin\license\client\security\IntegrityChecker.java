package com.robin.license.client.security;

import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.security.MessageDigest;
import java.util.HashMap;
import java.util.Map;

/**
 * 字节码完整性检查器
 * 防止运行时代码被篡改
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class IntegrityChecker {
    
    private static final IntegrityChecker INSTANCE = new IntegrityChecker();
    
    // 关键类的预期哈希值（在构建时计算）
    private static final Map<String, String> CLASS_HASHES = new HashMap<>();
    
    static {
        // 这些哈希值应该在构建时自动生成
        // 这里使用占位符，实际部署时需要替换为真实的哈希值
        CLASS_HASHES.put("com.robin.license.client.sdk.LicenseSDK", "PLACEHOLDER_HASH_1");
        CLASS_HASHES.put("com.robin.license.client.core.LicenseValidator", "PLACEHOLDER_HASH_2");
        CLASS_HASHES.put("com.robin.license.client.security.CipherUtil", "PLACEHOLDER_HASH_3");
        CLASS_HASHES.put("com.robin.license.client.network.LicenseClient", "PLACEHOLDER_HASH_4");
    }
    
    private volatile boolean integrityVerified = false;
    private final Object verificationLock = new Object();
    
    private IntegrityChecker() {
        // 私有构造函数
    }
    
    public static IntegrityChecker getInstance() {
        return INSTANCE;
    }
    
    /**
     * 验证关键类的完整性
     * 
     * @return 验证是否通过
     */
    public boolean verifyIntegrity() {
        if (integrityVerified) {
            return true;
        }
        
        synchronized (verificationLock) {
            if (integrityVerified) {
                return true;
            }
            
            log.info("开始验证字节码完整性...");
            
            try {
                for (Map.Entry<String, String> entry : CLASS_HASHES.entrySet()) {
                    String className = entry.getKey();
                    String expectedHash = entry.getValue();
                    
                    if (!verifyClassIntegrity(className, expectedHash)) {
                        log.error("类完整性验证失败: {}", className);
                        return false;
                    }
                }
                
                integrityVerified = true;
                log.info("字节码完整性验证通过");
                return true;
                
            } catch (Exception e) {
                log.error("完整性验证过程中发生异常", e);
                return false;
            }
        }
    }
    
    /**
     * 验证单个类的完整性
     * 
     * @param className 类名
     * @param expectedHash 期望的哈希值
     * @return 验证是否通过
     */
    private boolean verifyClassIntegrity(String className, String expectedHash) {
        try {
            // 跳过占位符哈希值的验证
            if ("PLACEHOLDER_HASH_1".equals(expectedHash) || 
                "PLACEHOLDER_HASH_2".equals(expectedHash) ||
                "PLACEHOLDER_HASH_3".equals(expectedHash) ||
                "PLACEHOLDER_HASH_4".equals(expectedHash)) {
                log.debug("跳过占位符哈希值的验证: {}", className);
                return true;
            }
            
            // 获取类的字节码
            String resourcePath = "/" + className.replace('.', '/') + ".class";
            InputStream inputStream = IntegrityChecker.class.getResourceAsStream(resourcePath);
            
            if (inputStream == null) {
                log.warn("无法找到类文件: {}", className);
                return false;
            }
            
            // 计算字节码的哈希值
            String actualHash = calculateHash(inputStream);
            inputStream.close();
            
            // 比较哈希值
            boolean matches = expectedHash.equals(actualHash);
            if (!matches) {
                log.error("类 {} 的哈希值不匹配. 期望: {}, 实际: {}", className, expectedHash, actualHash);
            }
            
            return matches;
            
        } catch (Exception e) {
            log.error("验证类 {} 完整性时发生异常", className, e);
            return false;
        }
    }
    
    /**
     * 计算输入流的SHA-256哈希值
     * 
     * @param inputStream 输入流
     * @return 哈希值
     */
    private String calculateHash(InputStream inputStream) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] buffer = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            digest.update(buffer, 0, bytesRead);
        }
        
        byte[] hashBytes = digest.digest();
        StringBuilder hexString = new StringBuilder();
        
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return hexString.toString();
    }
    
    /**
     * 强制重新验证完整性
     */
    public void forceReVerification() {
        synchronized (verificationLock) {
            integrityVerified = false;
            log.info("强制重新验证完整性");
        }
    }
    
    /**
     * 检查是否已通过完整性验证
     * 
     * @return 是否已验证
     */
    public boolean isIntegrityVerified() {
        return integrityVerified;
    }
    
    /**
     * 运行时完整性检查
     * 在关键方法执行前调用
     */
    public void runtimeIntegrityCheck() {
        if (!integrityVerified && !verifyIntegrity()) {
            throw new SecurityException("运行时完整性检查失败，检测到代码被篡改");
        }
    }
    
    /**
     * 获取类的当前哈希值（用于调试）
     * 
     * @param className 类名
     * @return 当前哈希值
     */
    public String getCurrentClassHash(String className) {
        try {
            String resourcePath = "/" + className.replace('.', '/') + ".class";
            InputStream inputStream = IntegrityChecker.class.getResourceAsStream(resourcePath);
            
            if (inputStream == null) {
                return null;
            }
            
            String hash = calculateHash(inputStream);
            inputStream.close();
            return hash;
            
        } catch (Exception e) {
            log.error("获取类 {} 哈希值时发生异常", className, e);
            return null;
        }
    }
    
    /**
     * 更新类的期望哈希值（仅用于开发阶段）
     * 
     * @param className 类名
     * @param hash 新的哈希值
     */
    public void updateExpectedHash(String className, String hash) {
        CLASS_HASHES.put(className, hash);
        log.debug("更新类 {} 的期望哈希值: {}", className, hash);
    }
}
