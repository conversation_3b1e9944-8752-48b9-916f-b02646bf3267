package com.robin.license.server.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * 客户端密钥对实体
 * 存储每个客户端的独立RSA密钥对
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Entity
@Table(name = "client_key_pair")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ClientKeyPair {
    
    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 客户端ID（机器指纹）
     */
    @Column(name = "client_id", nullable = false, unique = true, length = 64)
    private String clientId;
    
    /**
     * 私钥（PEM格式）
     */
    @Column(name = "private_key", nullable = false, columnDefinition = "TEXT")
    private String privateKeyPem;
    
    /**
     * 公钥（PEM格式）
     */
    @Column(name = "public_key", nullable = false, columnDefinition = "TEXT")
    private String publicKeyPem;
    
    /**
     * 密钥长度
     */
    @Column(name = "key_size", nullable = false)
    private Integer keySize;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    private LocalDateTime createTime;
    
    /**
     * 最后使用时间
     */
    @Column(name = "last_used_time")
    private LocalDateTime lastUsedTime;
    
    /**
     * 是否已撤销
     */
    @Column(name = "revoked", nullable = false)
    private Boolean revoked;
    
    /**
     * 撤销时间
     */
    @Column(name = "revoked_time")
    private LocalDateTime revokedTime;
    
    /**
     * 备注信息
     */
    @Column(name = "remarks", length = 500)
    private String remarks;
    
    /**
     * 创建时间自动设置
     */
    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createTime == null) {
            createTime = now;
        }
        if (revoked == null) {
            revoked = false;
        }
    }
    
    /**
     * 更新最后使用时间
     */
    public void updateLastUsedTime() {
        this.lastUsedTime = LocalDateTime.now();
    }
    
    /**
     * 撤销密钥对
     */
    public void revoke() {
        this.revoked = true;
        this.revokedTime = LocalDateTime.now();
    }
    
    /**
     * 检查密钥对是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return !revoked;
    }
}
