package com.robin.license.client.hardware;

import com.robin.license.common.dto.MachineInfo;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * 机器信息收集器
 * 跨平台收集机器硬件信息用于生成唯一标识
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class MachineInfoCollector {
    
    private static final String OS_NAME = System.getProperty("os.name").toLowerCase();
    
    /**
     * 收集完整的机器信息
     * 
     * @return 机器信息对象
     */
    public static MachineInfo collectMachineInfo() {
        MachineInfo machineInfo = MachineInfo.builder()
                .collectTime(LocalDateTime.now())
                .osName(System.getProperty("os.name"))
                .osVersion(System.getProperty("os.version"))
                .osArch(System.getProperty("os.arch"))
                .javaVersion(System.getProperty("java.version"))
                .javaVendor(System.getProperty("java.vendor"))
                .userName(System.getProperty("user.name"))
                .userHome(System.getProperty("user.home"))
                .workingDir(System.getProperty("user.dir"))
                .timezone(System.getProperty("user.timezone"))
                .memorySize(Runtime.getRuntime().maxMemory() / 1024 / 1024)
                .processorCount(Runtime.getRuntime().availableProcessors())
                .build();
        
        try {
            // 获取主机名和IP地址
            InetAddress localHost = InetAddress.getLocalHost();
            machineInfo.setHostname(localHost.getHostName());
            machineInfo.setIpAddress(localHost.getHostAddress());
            
            // 获取MAC地址
            NetworkInterface network = NetworkInterface.getByInetAddress(localHost);
            if (network != null) {
                byte[] mac = network.getHardwareAddress();
                if (mac != null) {
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < mac.length; i++) {
                        sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
                    }
                    machineInfo.setMacAddress(sb.toString());
                }
            }
        } catch (Exception e) {
            log.warn("获取网络信息失败", e);
        }
        
        // 获取机器唯一标识
        machineInfo.setMachineId(getMachineId());
        
        // 获取CPU序列号
        machineInfo.setCpuSerial(getCpuSerial());
        
        // 获取主板序列号
        machineInfo.setMotherboardSerial(getMotherboardSerial());
        
        // 获取硬盘序列号
        machineInfo.setDiskSerial(getDiskSerial());
        
        // 生成机器指纹
        machineInfo.generateFingerprint();
        
        return machineInfo;
    }
    
    /**
     * 获取机器唯一标识
     * 
     * @return 机器ID
     */
    public static String getMachineId() {
        try {
            if (isWindows()) {
                return getWindowsMachineId();
            } else if (isLinux()) {
                return getLinuxMachineId();
            } else if (isMacOS()) {
                return getMacOSMachineId();
            }
        } catch (Exception e) {
            log.error("获取机器ID失败", e);
        }
        return null;
    }
    
    /**
     * 获取CPU序列号
     * 
     * @return CPU序列号
     */
    public static String getCpuSerial() {
        try {
            if (isWindows()) {
                return executeCommand(Arrays.asList("wmic", "cpu", "get", "ProcessorId", "/value"));
            } else if (isLinux()) {
                return executeCommand(Arrays.asList("dmidecode", "-t", "processor"));
            } else if (isMacOS()) {
                return executeCommand(Arrays.asList("system_profiler", "SPHardwareDataType"));
            }
        } catch (Exception e) {
            log.error("获取CPU序列号失败", e);
        }
        return null;
    }
    
    /**
     * 获取主板序列号
     * 
     * @return 主板序列号
     */
    public static String getMotherboardSerial() {
        try {
            if (isWindows()) {
                return executeCommand(Arrays.asList("wmic", "baseboard", "get", "SerialNumber", "/value"));
            } else if (isLinux()) {
                return executeCommand(Arrays.asList("dmidecode", "-t", "baseboard"));
            } else if (isMacOS()) {
                return executeCommand(Arrays.asList("system_profiler", "SPHardwareDataType"));
            }
        } catch (Exception e) {
            log.error("获取主板序列号失败", e);
        }
        return null;
    }
    
    /**
     * 获取硬盘序列号
     * 
     * @return 硬盘序列号
     */
    public static String getDiskSerial() {
        try {
            if (isWindows()) {
                return executeCommand(Arrays.asList("wmic", "diskdrive", "get", "SerialNumber", "/value"));
            } else if (isLinux()) {
                return executeCommand(Arrays.asList("lsblk", "-o", "SERIAL"));
            } else if (isMacOS()) {
                return executeCommand(Arrays.asList("system_profiler", "SPStorageDataType"));
            }
        } catch (Exception e) {
            log.error("获取硬盘序列号失败", e);
        }
        return null;
    }
    
    /**
     * Windows系统获取机器ID
     */
    private static String getWindowsMachineId() throws Exception {
        String result = executeCommand(Arrays.asList("reg", "query", 
                "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography", "/v", "MachineGuid"));
        if (result != null && result.contains("MachineGuid")) {
            String[] lines = result.split("\n");
            for (String line : lines) {
                if (line.contains("MachineGuid")) {
                    String[] parts = line.trim().split("\\s+");
                    if (parts.length >= 3) {
                        return parts[2];
                    }
                }
            }
        }
        return null;
    }
    
    /**
     * Linux系统获取机器ID
     */
    private static String getLinuxMachineId() throws Exception {
        // 尝试从 /var/lib/dbus/machine-id 读取
        File machineIdFile = new File("/var/lib/dbus/machine-id");
        if (!machineIdFile.exists()) {
            machineIdFile = new File("/etc/machine-id");
        }
        
        if (machineIdFile.exists()) {
            try (BufferedReader reader = new BufferedReader(new FileReader(machineIdFile))) {
                return reader.readLine();
            }
        }
        return null;
    }
    
    /**
     * macOS系统获取机器ID
     */
    private static String getMacOSMachineId() throws Exception {
        String result = executeCommand(Arrays.asList("ioreg", "-rd1", "-c", "IOPlatformExpertDevice"));
        if (result != null && result.contains("IOPlatformUUID")) {
            String[] lines = result.split("\n");
            for (String line : lines) {
                if (line.contains("IOPlatformUUID")) {
                    int start = line.indexOf("\"") + 1;
                    int end = line.lastIndexOf("\"");
                    if (start > 0 && end > start) {
                        return line.substring(start, end);
                    }
                }
            }
        }
        return null;
    }
    
    /**
     * 执行系统命令
     * 
     * @param command 命令列表
     * @return 命令输出
     */
    private static String executeCommand(List<String> command) {
        try {
            ProcessBuilder pb = new ProcessBuilder(command);
            Process process = pb.start();
            
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            process.waitFor();
            return output.toString().trim();
        } catch (Exception e) {
            log.error("执行命令失败: {}", command, e);
            return null;
        }
    }
    
    /**
     * 判断是否为Windows系统
     */
    private static boolean isWindows() {
        return OS_NAME.contains("win");
    }
    
    /**
     * 判断是否为Linux系统
     */
    private static boolean isLinux() {
        return OS_NAME.contains("nix") || OS_NAME.contains("nux") || OS_NAME.contains("aix");
    }
    
    /**
     * 判断是否为macOS系统
     */
    private static boolean isMacOS() {
        return OS_NAME.contains("mac");
    }
}
