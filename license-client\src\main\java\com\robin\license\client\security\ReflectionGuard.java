package com.robin.license.client.security;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 反射检测和防护器
 * 检测并阻止关键方法被反射调用
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public class ReflectionGuard {
    
    private static final ReflectionGuard INSTANCE = new ReflectionGuard();
    
    // 受保护的关键类
    private static final Set<String> PROTECTED_CLASSES = new HashSet<>(Arrays.asList(
        "com.robin.license.client.sdk.LicenseSDK",
        "com.robin.license.client.core.LicenseValidator",
        "com.robin.license.client.security.CipherUtil",
        "com.robin.license.client.network.LicenseClient",
        "com.robin.license.client.security.SecurityManager"
    ));
    
    // 受保护的关键方法
    private static final Set<String> PROTECTED_METHODS = new HashSet<>(Arrays.asList(
        "retKeyword",
        "validateLicense",
        "syncLicense",
        "checkIntegrity",
        "verifyCallStack",
        "getInstance"
    ));
    
    // 允许的调用栈模式
    private static final Set<String> ALLOWED_CALL_PATTERNS = new HashSet<>(Arrays.asList(
        "com.robin.license.client.sdk",
        "com.robin.license.client.core",
        "com.robin.license.client.network",
        "com.robin.license.client.security"
    ));
    
    private volatile boolean guardEnabled = true;
    private final ThreadLocal<Boolean> bypassFlag = new ThreadLocal<>();
    
    private ReflectionGuard() {
        // 私有构造函数
    }
    
    public static ReflectionGuard getInstance() {
        return INSTANCE;
    }
    
    /**
     * 检查反射调用是否被允许
     * 
     * @param targetClass 目标类
     * @param methodName 方法名
     * @return 是否允许
     */
    public boolean checkReflectionAccess(Class<?> targetClass, String methodName) {
        if (!guardEnabled || Boolean.TRUE.equals(bypassFlag.get())) {
            return true;
        }
        
        String className = targetClass.getName();
        
        // 检查是否为受保护的类
        if (PROTECTED_CLASSES.contains(className)) {
            log.warn("检测到对受保护类的反射访问: {} -> {}", className, methodName);
            
            // 检查是否为受保护的方法
            if (PROTECTED_METHODS.contains(methodName)) {
                log.error("阻止对关键方法的反射调用: {}.{}", className, methodName);
                
                // 验证调用栈
                if (!verifyCallStack()) {
                    throw new SecurityException("非法的反射调用被阻止: " + className + "." + methodName);
                }
            }
        }
        
        return true;
    }
    
    /**
     * 验证调用栈的合法性
     * 
     * @return 调用栈是否合法
     */
    private boolean verifyCallStack() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        // 检查调用栈中是否包含可疑的反射调用
        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            String methodName = element.getMethodName();
            
            // 检测反射相关的调用
            if (className.startsWith("java.lang.reflect") || 
                methodName.equals("invoke") || 
                methodName.equals("newInstance")) {
                
                // 检查是否来自允许的包
                boolean fromAllowedPackage = false;
                for (StackTraceElement caller : stackTrace) {
                    String callerClass = caller.getClassName();
                    for (String allowedPattern : ALLOWED_CALL_PATTERNS) {
                        if (callerClass.startsWith(allowedPattern)) {
                            fromAllowedPackage = true;
                            break;
                        }
                    }
                    if (fromAllowedPackage) break;
                }
                
                if (!fromAllowedPackage) {
                    log.error("检测到来自非法包的反射调用: {}", className);
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * 临时绕过反射检查（仅供内部使用）
     * 
     * @param action 要执行的动作
     * @param <T> 返回类型
     * @return 执行结果
     */
    public <T> T bypassReflectionCheck(java.util.function.Supplier<T> action) {
        bypassFlag.set(true);
        try {
            return action.get();
        } finally {
            bypassFlag.remove();
        }
    }
    
    /**
     * 启用/禁用反射防护
     * 
     * @param enabled 是否启用
     */
    public void setGuardEnabled(boolean enabled) {
        this.guardEnabled = enabled;
        log.info("反射防护状态: {}", enabled ? "启用" : "禁用");
    }
    
    /**
     * 检查方法是否被反射调用
     * 
     * @return 是否被反射调用
     */
    public static boolean isReflectionCall() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        for (StackTraceElement element : stackTrace) {
            if (element.getClassName().startsWith("java.lang.reflect") ||
                element.getMethodName().equals("invoke")) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取调用者信息
     * 
     * @return 调用者类名
     */
    public static String getCallerInfo() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        // 跳过当前方法和getStackTrace方法
        if (stackTrace.length > 2) {
            StackTraceElement caller = stackTrace[2];
            return caller.getClassName() + "." + caller.getMethodName();
        }
        
        return "Unknown";
    }
    
    /**
     * 验证调用者是否合法
     * 
     * @param expectedPackage 期望的包名前缀
     * @return 是否合法
     */
    public static boolean verifyCallerPackage(String expectedPackage) {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        
        // 跳过当前方法和getStackTrace方法
        if (stackTrace.length > 2) {
            StackTraceElement caller = stackTrace[2];
            return caller.getClassName().startsWith(expectedPackage);
        }
        
        return false;
    }
}
