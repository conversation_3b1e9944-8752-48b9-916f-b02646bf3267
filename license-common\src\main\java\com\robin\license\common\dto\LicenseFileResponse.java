package com.robin.license.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 许可证文件响应DTO
 * 用于返回许可证文件内容和公钥内容
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LicenseFileResponse {
    
    /**
     * 许可证文件内容（Base64编码）
     */
    private String licenseFileContent;
    
    /**
     * 公钥内容（PEM格式）
     */
    private String publicKeyContent;
    
    /**
     * 许可证ID
     */
    private String licenseId;
    
    /**
     * 机器ID
     */
    private String machineId;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 产品名称
     */
    private String productName;
    
    /**
     * 过期时间（时间戳）
     */
    private Long expireTime;
}
