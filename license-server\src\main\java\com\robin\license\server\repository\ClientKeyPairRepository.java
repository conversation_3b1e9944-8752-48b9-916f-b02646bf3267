package com.robin.license.server.repository;

import com.robin.license.server.entity.ClientKeyPair;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 客户端密钥对数据访问接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Repository
public interface ClientKeyPairRepository extends JpaRepository<ClientKeyPair, Long> {
    
    /**
     * 根据客户端ID查找密钥对
     * 
     * @param clientId 客户端ID
     * @return 密钥对
     */
    Optional<ClientKeyPair> findByClientId(String clientId);
    
    /**
     * 根据客户端ID查找有效的密钥对
     * 
     * @param clientId 客户端ID
     * @return 有效的密钥对
     */
    @Query("SELECT ckp FROM ClientKeyPair ckp WHERE ckp.clientId = :clientId AND ckp.revoked = false")
    Optional<ClientKeyPair> findValidByClientId(@Param("clientId") String clientId);
    
    /**
     * 检查客户端ID是否存在
     * 
     * @param clientId 客户端ID
     * @return 是否存在
     */
    boolean existsByClientId(String clientId);
    
    /**
     * 检查客户端是否有有效的密钥对
     * 
     * @param clientId 客户端ID
     * @return 是否有有效密钥对
     */
    @Query("SELECT COUNT(ckp) > 0 FROM ClientKeyPair ckp WHERE ckp.clientId = :clientId AND ckp.revoked = false")
    boolean existsValidByClientId(@Param("clientId") String clientId);
}
