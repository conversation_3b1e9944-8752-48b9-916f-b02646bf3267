package com.robin.license.server.service;

import com.google.gson.Gson;
import com.robin.license.common.constants.LicenseConstants;
import com.robin.license.common.crypto.CryptoUtil;
import com.robin.license.common.dto.LicenseFileResponse;
import com.robin.license.common.dto.LicenseInfo;
import com.robin.license.common.dto.MachineInfo;
import com.robin.license.common.dto.SimpleMachineInfo;
import com.robin.license.common.exception.LicenseException;
import com.robin.license.server.entity.License;
import com.robin.license.server.repository.LicenseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.DataOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 许可证服务类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Slf4j
public class LicenseService {
    
    @Autowired
    private LicenseRepository licenseRepository;
    
    @Autowired
    private KeyManagementService keyManagementService;
    
    private final Gson gson = new Gson();
    
    /**
     * 生成许可证
     *
     * @param licenseInfo 许可证信息
     * @return 生成的许可证
     */
    @Transactional
    public License generateLicense(LicenseInfo licenseInfo) {
        // 生成唯一的许可证ID
        String licenseId = generateLicenseId();

        // 创建许可证实体
        License license = License.builder()
                .licenseId(licenseId)
                .customerName(licenseInfo.getCustomerName())
                .productName(licenseInfo.getProductName())
                .licenseType(licenseInfo.getLicenseType())
                .machineId(licenseInfo.getMachineId())
                .status(LicenseConstants.Status.VALID)
                .expireTime(licenseInfo.getExpireTime())
                .build();

        // 保存许可证
        License savedLicense = licenseRepository.save(license);

        log.info("生成许可证成功，许可证ID: {}, 客户: {}, 产品: {}",
                licenseId, licenseInfo.getCustomerName(), licenseInfo.getProductName());

        return savedLicense;
    }
    

    
    /**
     * 验证许可证（使用机器指纹）
     *
     * @param licenseId 许可证ID
     * @param machineInfo 机器信息
     * @return 验证结果
     */
    @Transactional
    public boolean verifyLicense(String licenseId, MachineInfo machineInfo) {
        String machineFingerprint = machineInfo.generateFingerprint();
        return verifyLicenseByFingerprint(licenseId, machineFingerprint);
    }

    /**
     * 验证许可证（兼容旧接口）
     *
     * @param licenseId 许可证ID
     * @param machineId 机器ID
     * @return 验证结果
     */
    @Transactional
    public boolean verifyLicense(String licenseId, String machineId) {
        // 兼容旧接口，直接使用machineId作为指纹
        return verifyLicenseByFingerprint(licenseId, machineId);
    }

    /**
     * 使用指纹验证许可证的核心方法
     *
     * @param licenseId 许可证ID
     * @param machineFingerprint 机器指纹
     * @return 验证结果
     */
    public boolean verifyLicenseByFingerprint(String licenseId, String machineFingerprint) {
        try {
            Optional<License> licenseOpt = licenseRepository.findByLicenseId(licenseId);
            if (!licenseOpt.isPresent()) {
                log.warn("许可证不存在: {}", licenseId);
                return false;
            }

            License license = licenseOpt.get();

            // 检查许可证状态
            if (!license.isValid()) {
                log.warn("许可证状态无效: {}", licenseId);
                return false;
            }

            // 检查是否过期
            if (license.isExpired()) {
                log.warn("许可证已过期: {}", licenseId);
                return false;
            }

            // 检查机器绑定（使用指纹）
            if (license.getMachineId() != null && !license.getMachineId().equals(machineFingerprint)) {
                log.warn("机器指纹不匹配，许可证ID: {}, 期望: {}, 实际: {}",
                        licenseId, license.getMachineId(), machineFingerprint);
                return false;
            }

            // 更新验证信息
            license.incrementVerifyCount();
            licenseRepository.save(license);

            log.info("许可证验证成功: {}", licenseId);
            return true;

        } catch (Exception e) {
            log.error("许可证验证失败: " + licenseId, e);
            return false;
        }
    }
    
    /**
     * 获取许可证信息
     * 
     * @param licenseId 许可证ID
     * @return 许可证信息
     */
    public LicenseInfo getLicenseInfo(String licenseId) {
        Optional<License> licenseOpt = licenseRepository.findByLicenseId(licenseId);
        if (!licenseOpt.isPresent()) {
            throw new LicenseException.LicenseNotFoundException("许可证不存在: " + licenseId);
        }
        
        return convertToLicenseInfo(licenseOpt.get());
    }
    

    
    /**
     * 撤销许可证
     * 
     * @param licenseId 许可证ID
     */
    @Transactional
    public void revokeLicense(String licenseId) {
        Optional<License> licenseOpt = licenseRepository.findByLicenseId(licenseId);
        if (!licenseOpt.isPresent()) {
            throw new LicenseException.LicenseNotFoundException("许可证不存在: " + licenseId);
        }
        
        License license = licenseOpt.get();
        license.setStatus(LicenseConstants.Status.INVALID);
        licenseRepository.save(license);
        
        log.info("许可证已撤销: {}", licenseId);
    }
    


    /**
     * 许可证同步（使用简化机器信息）
     *
     * @param simpleMachineInfo 简化的机器信息
     * @return 许可证文件响应
     */
    @Transactional
    public LicenseFileResponse syncLicense(SimpleMachineInfo simpleMachineInfo) {
        try {
            // 生成机器指纹用于绑定
            String machineFingerprint = simpleMachineInfo.generateFingerprint();
            String machineId = simpleMachineInfo.getMachineId();

            // 检查机器是否已经注册过（使用指纹查找）
            List<License> existingLicenses = licenseRepository.findByMachineId(machineFingerprint);
            for (License license : existingLicenses) {
                if (license.isValid() && !license.isExpired()) {
                    log.info("机器已存在有效许可证，返回最新证书: {}", machineId);
                    // 更新验证时间
                    license.incrementVerifyCount();
                    licenseRepository.save(license);
                    return generateLicenseFileResponse(license);
                }
            }

            // 机器未注册或许可证已过期，创建新的许可证
            LicenseInfo licenseInfo = new LicenseInfo();
            licenseInfo.setCustomerName("Auto-Generated-" + machineId.substring(0, 8));
            licenseInfo.setProductName("Default Product");
            licenseInfo.setLicenseType(LicenseConstants.Type.TRIAL);
            licenseInfo.setMachineId(machineFingerprint); // 使用指纹而不是原始machineId

            // 设置试用期30天
            LocalDateTime expireTime = LocalDateTime.now().plusDays(30);
            licenseInfo.setExpireTime(expireTime);

            // 生成许可证
            License license = generateLicense(licenseInfo);

            log.info("为机器创建新许可证: {}, 机器ID: {}, 指纹: {}", license.getLicenseId(), machineId, machineFingerprint);
            return generateLicenseFileResponse(license);

        } catch (Exception e) {
            log.error("许可证同步失败: " + simpleMachineInfo.getMachineId(), e);
            return null;
        }
    }

    /**
     * 生成许可证ID
     *
     * @return 许可证ID
     */
    private String generateLicenseId() {
        return "LIC-" + UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }
    

    


    /**
     * 生成许可证文件响应
     *
     * @param license 许可证实体
     * @return 许可证文件响应
     */
    private LicenseFileResponse generateLicenseFileResponse(License license) {
        try {
            // 生成许可证文件内容（使用客户端特定的密钥）
            byte[] licenseFileContent = generateLicenseFileContent(license);

            // 获取客户端特定的公钥内容
            String publicKeyContent = keyManagementService.getClientPublicKeyPem(license.getMachineId());

            return LicenseFileResponse.builder()
                    .licenseFileContent(CryptoUtil.base64Encode(licenseFileContent))
                    .publicKeyContent(publicKeyContent)
                    .licenseId(license.getLicenseId())
                    .machineId(license.getMachineId())
                    .customerName(license.getCustomerName())
                    .productName(license.getProductName())
                    .expireTime(license.getExpireTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())
                    .build();

        } catch (Exception e) {
            log.error("生成许可证文件响应失败: " + license.getLicenseId(), e);
            throw new RuntimeException("生成许可证文件响应失败", e);
        }
    }

    /**
     * 生成许可证文件内容（字节数组）
     *
     * @param license 许可证实体
     * @return 许可证文件内容
     */
    private byte[] generateLicenseFileContent(License license) throws IOException {
        // 构建许可证数据
        String licenseData = buildLicenseData(license);

        // 生成机器密码（使用机器ID）
        String machinePassword = generateMachinePassword(license.getMachineId());

        // 加密许可证数据
        byte[] encryptedData = CryptoUtil.encryptAES(licenseData.getBytes(StandardCharsets.UTF_8),
                machinePassword.getBytes());

        // 生成数字签名（使用客户端特定的私钥）
        PrivateKey privateKey = keyManagementService.getClientPrivateKey(license.getMachineId());
        byte[] signature = CryptoUtil.signRSA(encryptedData, privateKey);

        // 使用内存流生成文件内容
        java.io.ByteArrayOutputStream byteArrayOutputStream = new java.io.ByteArrayOutputStream();
        try (DataOutputStream outputStream = new DataOutputStream(byteArrayOutputStream)) {
            // 写入MZ头
            outputStream.write(LicenseConstants.Bytes.MZ_HEADER);

            // 写入加密数据长度
            outputStream.writeInt(encryptedData.length);

            // 写入加密数据
            outputStream.write(encryptedData);

            // 写入填充字节
            outputStream.write(LicenseConstants.Bytes.DATA_PADDING);

            // 写入签名长度
            outputStream.writeInt(signature.length);

            // 写入数字签名
            outputStream.write(signature);

            // 写入文件结束标记
            outputStream.write(LicenseConstants.Bytes.FILE_ENDING);
        }

        byte[] fileContent = byteArrayOutputStream.toByteArray();
        log.info("许可证文件内容生成成功，大小: {} 字节", fileContent.length);

        return fileContent;
    }

    /**
     * 转换为许可证信息DTO
     *
     * @param license 许可证实体
     * @return 许可证信息DTO
     */
    private LicenseInfo convertToLicenseInfo(License license) {
        return LicenseInfo.builder()
                .licenseId(license.getLicenseId())
                .customerName(license.getCustomerName())
                .productName(license.getProductName())
                .licenseType(license.getLicenseType())
                .machineId(license.getMachineId())
                .status(license.getStatus())
                .expireTime(license.getExpireTime())
                .createTime(license.getCreateTime())
                .build();
    }



    /**
     * 构建许可证数据字符串
     *
     * @param license 许可证实体
     * @return 许可证数据字符串
     */
    private String buildLicenseData(License license) {
        // 格式: machineId;expireTime;status;customerName;productName;licenseType
        StringBuilder builder = new StringBuilder();
        builder.append(license.getMachineId()).append(";");
        builder.append(license.getExpireTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()).append(";");
        builder.append(license.getStatus()).append(";");
        builder.append(license.getCustomerName()).append(";");
        builder.append(license.getProductName()).append(";");
        builder.append(license.getLicenseType());

        return builder.toString();
    }

    /**
     * 生成机器密码（基于机器指纹）
     *
     * @param machineFingerprint 机器指纹
     * @return 机器密码
     */
    private String generateMachinePassword(String machineFingerprint) {
        if (machineFingerprint == null || machineFingerprint.length() < 16) {
            return LicenseConstants.Crypto.DEFAULT_KEY;
        }

        // 使用机器指纹的前16位作为密码
        String password = machineFingerprint.replaceAll("[\\-\\s]", "");
        if (password.length() > 16) {
            password = password.substring(0, 16);
        } else if (password.length() < 16) {
            password = String.format("%-16s", password).replace(' ', '0');
        }

        return password;
    }
}
